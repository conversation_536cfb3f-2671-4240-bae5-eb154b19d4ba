package com.guducat.collegeWeb;

import io.github.cdimascio.dotenv.Dotenv;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@MapperScan("com.guducat.collegeWeb.mapper")
public class CollegeWebApplication {

    public static void main(String[] args) {
        // 在启动Spring应用之前设置系统属性
        loadEnvToSystemProperties();

        // 启动Spring应用
        SpringApplication.run(CollegeWebApplication.class, args);
    }

    private static void loadEnvToSystemProperties() {
        try {
            System.out.println("正在尝试加载.env文件...");
            System.out.println("当前工作目录: " + System.getProperty("user.dir"));

            Dotenv dotenv = null;

            // 尝试多个可能的路径
            String[] possiblePaths = {"./", "../", "../../"};

            for (String path : possiblePaths) {
                try {
                    System.out.println("尝试路径: " + path);
                    dotenv = Dotenv.configure()
                            .directory(path)
                            .filename(".env")
                            .load();
                    System.out.println("成功从路径 " + path + " 加载.env文件");
                    break;
                } catch (Exception e) {
                    System.out.println("路径 " + path + " 加载失败: " + e.getMessage());
                }
            }

            if (dotenv == null) {
                System.err.println("所有路径都无法找到.env文件，尝试忽略缺失文件的方式加载");
                dotenv = Dotenv.configure()
                        .directory("./")
                        .filename(".env")
                        .ignoreIfMissing()
                        .load();
            }

            // 将.env文件中的变量设置为系统属性
            dotenv.entries().forEach(entry -> {
                String key = entry.getKey();
                String value = entry.getValue();
                System.setProperty(key, value);
            });

            System.out.println("成功加载.env文件，包含 " + dotenv.entries().size() + " 个环境变量");

            // 打印重要的数据库配置（隐藏敏感信息）
            System.out.println("DB_URL=" + System.getProperty("DB_URL"));
            System.out.println("DB_USER=" + System.getProperty("DB_USER"));
            System.out.println("DB_PASSWORD=***隐藏***");
            System.out.println("DB_DRIVER=" + System.getProperty("DB_DRIVER"));

        } catch (Exception e) {
            System.err.println("加载.env文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

package com.guducat.collegeWeb.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * CORS跨域配置类
 * 配置允许的域名、请求方法、请求头等
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns(
                    "http://localhost:*",
                    "https://college.guducat.cn",
                    "https://m.college.guducat.cn",
                    "https://preview1.guducat.laffey.cc",
                    "https://*.guducat.cn",
                    "https://*.laffey.cc"
                ) // 使用allowedOriginPatterns支持通配符，更灵活
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH")
                .allowedHeaders("*")
                .exposedHeaders("satoken", "Authorization", "Content-Type", "X-Requested-With")
                .allowCredentials(true)
                .maxAge(7200); // 增加预检请求缓存时间到2小时

    }

}


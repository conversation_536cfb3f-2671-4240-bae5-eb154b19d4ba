package com.guducat.collegeWeb.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MinIO配置类
 * 配置MinIO客户端连接参数
 */
@Configuration
public class MinIOConfig {

    @Value("${MINIO_ENDPOINT:https://lobechat-s3.laffey.cc:49917}")
    private String endpoint;

    @Value("${MINIO_ACCESS_KEY:}")
    private String accessKey;

    @Value("${MINIO_SECRET_KEY:}")
    private String secretKey;

    /**
     * 创建MinIO客户端Bean
     * @return MinioClient实例
     */
    @Bean
    public MinioClient minioClient() {
        // 如果访问密钥为空，则不创建客户端
        if (accessKey == null || accessKey.trim().isEmpty() ||
            secretKey == null || secretKey.trim().isEmpty()) {
            throw new IllegalStateException("MinIO访问密钥未配置，请在.env文件中设置MINIO_ACCESS_KEY和MINIO_SECRET_KEY");
        }

        return MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
    }
}

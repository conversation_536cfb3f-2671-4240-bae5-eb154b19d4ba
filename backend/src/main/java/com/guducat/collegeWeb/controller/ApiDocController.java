package com.guducat.collegeWeb.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * API文档测试控制器
 * 用于测试SpringDoc OpenAPI是否正常工作
 */
@RestController
@RequestMapping("/api/doc-test")
@Tag(name = "API文档测试", description = "用于测试SpringDoc OpenAPI是否正常工作")
public class ApiDocController {

    /**
     * 测试API
     * @return 测试结果
     */
    @Operation(
        summary = "测试API",
        description = "用于测试SpringDoc OpenAPI是否正常工作"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "测试成功",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = Map.class)))
    })
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "SpringDoc OpenAPI测试成功");
        result.put("status", "ok");
        result.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(result);
    }

    /**
     * 测试CORS跨域配置
     * @return CORS测试结果
     */
    @Operation(
        summary = "测试CORS跨域配置",
        description = "用于测试CORS跨域配置是否正常工作，包括预检请求处理"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "CORS测试成功",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = Map.class)))
    })
    @GetMapping("/cors-test")
    public ResponseEntity<Map<String, Object>> corsTest() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "CORS跨域配置测试成功");
        result.put("status", "ok");
        result.put("timestamp", System.currentTimeMillis());
        result.put("cors_enabled", true);
        return ResponseEntity.ok(result);
    }
}

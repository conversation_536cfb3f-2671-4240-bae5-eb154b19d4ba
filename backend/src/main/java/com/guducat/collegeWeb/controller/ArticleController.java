package com.guducat.collegeWeb.controller;

import com.guducat.collegeWeb.dto.response.ArticleDetailDTO;
import com.guducat.collegeWeb.dto.response.ArticleListDTO;
import com.guducat.collegeWeb.dto.response.PageResultDTO;
import com.guducat.collegeWeb.service.ArticleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/articles")
@Tag(name = "文章管理", description = "提供文章查询、搜索等功能")
public class ArticleController {
    private final ArticleService articleService;

    public ArticleController(ArticleService articleService) {
        this.articleService = articleService;
    }

    @Operation(
        summary = "获取文章详情",
        description = "根据文章ID获取文章的详细信息，包括标题、内容、发布时间等"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取文章详情",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = ArticleDetailDTO.class))),
        @ApiResponse(responseCode = "404", description = "文章不存在")
    })
    @GetMapping("/{newsId}")
    public ResponseEntity<ArticleDetailDTO> getArticleDetail(
        @Parameter(description = "文章ID", required = true, example = "1001")
        @PathVariable Integer newsId) {
        ArticleDetailDTO dto = articleService.getArticleDetail(newsId);
        //验证收到请求
        System.out.println("Received request for newsId: " + newsId);

        return ResponseEntity.ok(dto);
    }

    @Operation(
        summary = "获取最新文章列表（分页）",
        description = "获取按发布时间排序的最新文章列表，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取文章列表",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = PageResultDTO.class)))
    })
    @GetMapping("/latest")
    public ResponseEntity<PageResultDTO<ArticleListDTO>> getLatestArticles(
            @Parameter(description = "页码（从1开始）", required = false, example = "1")
            @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页记录数", required = false, example = "10")
            @RequestParam(defaultValue = "10") int pageSize) {
        PageResultDTO<ArticleListDTO> result = articleService.getLatestArticles(pageNum, pageSize);
        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "根据分类获取文章列表（分页）",
        description = "根据指定的文章分类获取文章列表，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取分类文章列表",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = PageResultDTO.class)))
    })
    @GetMapping("/category/{category}")
    public ResponseEntity<PageResultDTO<ArticleListDTO>> getArticlesByCategory(
            @Parameter(description = "文章分类", required = true, example = "校园新闻")
            @PathVariable String category,
            @Parameter(description = "页码（从1开始）", required = false, example = "1")
            @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页记录数", required = false, example = "10")
            @RequestParam(defaultValue = "10") int pageSize) {
        PageResultDTO<ArticleListDTO> result = articleService.getArticlesByCategory(category, pageNum, pageSize);
        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "根据关键词搜索文章（分页）",
        description = "根据提供的关键词在文章标题和内容中进行搜索，返回匹配的文章列表，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取搜索结果",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = PageResultDTO.class)))
    })
    @GetMapping("/search")
    public ResponseEntity<PageResultDTO<ArticleListDTO>> searchArticles(
            @Parameter(description = "搜索关键词", required = true, example = "学院")
            @RequestParam String keyword,
            @Parameter(description = "页码（从1开始）", required = false, example = "1")
            @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页记录数", required = false, example = "10")
            @RequestParam(defaultValue = "10") int pageSize) {
        PageResultDTO<ArticleListDTO> result = articleService.searchArticlesByKeyword(keyword, pageNum, pageSize);
        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "根据学院/教务处筛选文章（分页）",
        description = "根据指定的学院/教务处等行政机构信息筛选文章，支持分页查询"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取筛选结果",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = PageResultDTO.class)))
    })
    @GetMapping("/section/{section}")
    public ResponseEntity<PageResultDTO<ArticleListDTO>> getArticlesBySection(
            @Parameter(description = "学院/教务处等行政机构信息", required = true, example = "人工智能学院")
            @PathVariable String section,
            @Parameter(description = "页码（从1开始）", required = false, example = "1")
            @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页记录数", required = false, example = "10")
            @RequestParam(defaultValue = "10") int pageSize) {
        PageResultDTO<ArticleListDTO> result = articleService.getArticlesBySection(section, pageNum, pageSize);
        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "获取所有可用的文章分类列表",
        description = "获取数据库中所有不重复的文章分类，用于筛选功能"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取分类列表",
            content = @Content(mediaType = "application/json",
            schema = @Schema(type = "array", implementation = String.class)))
    })
    @GetMapping("/categories")
    public ResponseEntity<List<String>> getAllCategories() {
        // 返回固定的分类列表
        List<String> categories = List.of(
            "校园快讯",
            "通知公告",
            "学术活动",
            "招生就业",
            "校园生活"
        );
        return ResponseEntity.ok(categories);
    }

    @Operation(
        summary = "获取所有可用的学院/部门列表",
        description = "获取数据库中所有不重复的学院/部门，用于筛选功能"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取学院/部门列表",
            content = @Content(mediaType = "application/json",
            schema = @Schema(type = "array", implementation = String.class)))
    })
    @GetMapping("/sections")
    public ResponseEntity<List<String>> getAllSections() {
        // 返回固定的学院/部门列表
        List<String> sections = List.of(
            "人工智能学院",
            "建筑与艺术学院",
            "数字商学院",
            "人文与教育学院",
            "卫生健康学院",
            "智能工程学院",
            "教务处",
            "学生处",
            "招生办",
            "图书馆"
        );
        return ResponseEntity.ok(sections);
    }

    @Operation(
        summary = "多条件搜索文章（分页）",
        description = "支持关键词、分类、学院的组合搜索，所有参数都是可选的"
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "成功获取搜索结果",
            content = @Content(mediaType = "application/json",
            schema = @Schema(implementation = PageResultDTO.class)))
    })
    @GetMapping("/search/advanced")
    public ResponseEntity<PageResultDTO<ArticleListDTO>> searchWithMultipleConditions(
            @Parameter(description = "搜索关键词", required = false, example = "学院")
            @RequestParam(required = false) String keyword,
            @Parameter(description = "文章分类", required = false, example = "校园新闻")
            @RequestParam(required = false) String category,
            @Parameter(description = "学院/部门", required = false, example = "人工智能学院")
            @RequestParam(required = false) String section,
            @Parameter(description = "页码（从1开始）", required = false, example = "1")
            @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页记录数", required = false, example = "10")
            @RequestParam(defaultValue = "10") int pageSize) {

        PageResultDTO<ArticleListDTO> result = articleService.searchWithMultipleConditions(
                keyword, category, section, pageNum, pageSize);
        return ResponseEntity.ok(result);
    }
}

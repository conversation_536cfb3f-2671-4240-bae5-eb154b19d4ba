package com.guducat.collegeWeb.controller;

import com.guducat.collegeWeb.entity.SchoolInfo;
import com.guducat.collegeWeb.entity.SchoolStatistics;
import com.guducat.collegeWeb.entity.SchoolHonors;
import com.guducat.collegeWeb.entity.SchoolAchievements;
import com.guducat.collegeWeb.service.SchoolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 学校信息控制器
 */
@RestController
@RequestMapping("/api/school")
@Tag(name = "学校信息管理", description = "学校基本信息、统计数据、荣誉、成就等相关接口")
public class SchoolController {

    private final SchoolService schoolService;

    public SchoolController(SchoolService schoolService) {
        this.schoolService = schoolService;
    }

    /**
     * 获取学校基本信息
     * @return 学校基本信息
     */
    @Operation(summary = "获取学校基本信息")
    @GetMapping("/info")
    public ResponseEntity<SchoolInfo> getSchoolInfo() {
        SchoolInfo schoolInfo = schoolService.getSchoolInfo();
        return ResponseEntity.ok(schoolInfo);
    }

    /**
     * 获取学校统计数据
     * @return 统计数据列表
     */
    @Operation(summary = "获取学校统计数据")
    @GetMapping("/statistics")
    public ResponseEntity<List<SchoolStatistics>> getSchoolStatistics() {
        List<SchoolStatistics> statistics = schoolService.getSchoolStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取学校荣誉数据
     * @return 荣誉数据列表
     */
    @Operation(summary = "获取学校荣誉数据")
    @GetMapping("/honors")
    public ResponseEntity<List<SchoolHonors>> getSchoolHonors() {
        List<SchoolHonors> honors = schoolService.getSchoolHonors();
        return ResponseEntity.ok(honors);
    }

    /**
     * 根据级别获取学校荣誉数据
     * @param category 荣誉级别
     * @return 荣誉数据列表
     */
    @Operation(summary = "根据级别获取学校荣誉")
    @GetMapping("/honors/category/{category}")
    public ResponseEntity<List<SchoolHonors>> getSchoolHonorsByCategory(
            @Parameter(description = "荣誉级别", example = "国家级")
            @PathVariable String category) {
        List<SchoolHonors> honors = schoolService.getSchoolHonorsByCategory(category);
        return ResponseEntity.ok(honors);
    }

    /**
     * 获取学校成就数据
     * @return 成就数据列表
     */
    @Operation(summary = "获取学校成就数据")
    @GetMapping("/achievements")
    public ResponseEntity<List<SchoolAchievements>> getSchoolAchievements() {
        List<SchoolAchievements> achievements = schoolService.getSchoolAchievements();
        return ResponseEntity.ok(achievements);
    }

    /**
     * 根据分类获取学校成就数据
     * @param category 成就分类
     * @return 成就数据列表
     */
    @GetMapping("/achievements/category/{category}")
    @Operation(summary = "根据分类获取学校成就", description = "根据成就分类获取对应成就数据")
    public ResponseEntity<List<SchoolAchievements>> getSchoolAchievementsByCategory(@PathVariable String category) {
        List<SchoolAchievements> achievements = schoolService.getSchoolAchievementsByCategory(category);
        return ResponseEntity.ok(achievements);
    }

    /**
     * 获取学校简介页面完整数据
     * @return 包含所有学校信息的综合数据
     */
    @GetMapping("/introduction")
    @Operation(summary = "获取学校简介页面数据", description = "获取学校简介页面所需的完整数据，包括基本信息、统计数据和成就")
    public ResponseEntity<Map<String, Object>> getSchoolIntroductionData() {
        Map<String, Object> data = new HashMap<>();
        data.put("schoolInfo", schoolService.getSchoolInfo());
        data.put("statistics", schoolService.getSchoolStatistics());
        data.put("achievements", schoolService.getSchoolAchievements());
        return ResponseEntity.ok(data);
    }

    @Operation(summary = "获取学校荣誉页面数据")
    @GetMapping("/honors-page")
    public ResponseEntity<Map<String, Object>> getSchoolHonorsPageData() {
        Map<String, Object> data = new HashMap<>();
        data.put("allHonors", schoolService.getSchoolHonors());
        data.put("nationalHonors", schoolService.getSchoolHonorsByCategory("国家级"));
        data.put("provincialHonors", schoolService.getSchoolHonorsByCategory("省级"));
        data.put("municipalHonors", schoolService.getSchoolHonorsByCategory("市级"));
        data.put("industryHonors", schoolService.getSchoolHonorsByCategory("行业级"));
        return ResponseEntity.ok(data);
    }
}

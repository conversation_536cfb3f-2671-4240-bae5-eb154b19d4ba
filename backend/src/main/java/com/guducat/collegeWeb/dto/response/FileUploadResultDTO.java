package com.guducat.collegeWeb.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件上传结果DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "文件上传结果")
public class FileUploadResultDTO {

    @Schema(description = "文件访问URL", example = "https://example.com/images/abc123.jpg")
    private String url;

    @Schema(description = "文件名", example = "abc123.jpg")
    private String fileName;

    @Schema(description = "原始文件名", example = "image.jpg")
    private String originalFileName;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "文件类型", example = "image/jpeg")
    private String contentType;

    @Schema(description = "图片alt属性（可选）", example = "图片描述")
    private String alt;

    @Schema(description = "图片href属性（可选）", example = "")
    private String href;
}

package com.guducat.collegeWeb.mapper;

import com.guducat.collegeWeb.entity.SchoolAchievements;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 学校成就数据访问接口
 */
@Mapper
public interface SchoolAchievementsMapper {

    /**
     * 获取所有启用的成就数据
     * @return 成就数据列表
     */
    @Results(id = "schoolAchievementsResultMap", value = {
        @Result(property = "id", column = "id", id = true),
        @Result(property = "achievementTitle", column = "achievement_title"),
        @Result(property = "achievementContent", column = "achievement_content"),
        @Result(property = "achievementCategory", column = "achievement_category"),
        @Result(property = "statistics", column = "statistics"),
        @Result(property = "displayOrder", column = "display_order"),
        @Result(property = "isActive", column = "is_active"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    @Select("SELECT * FROM school_achievements WHERE is_active = 1 ORDER BY display_order")
    List<SchoolAchievements> getAllActiveAchievements();

    /**
     * 根据分类获取成就数据
     * @param category 成就分类
     * @return 成就数据列表
     */
    @ResultMap("schoolAchievementsResultMap")
    @Select("SELECT * FROM school_achievements WHERE is_active = 1 AND achievement_category = #{category} ORDER BY display_order")
    List<SchoolAchievements> getAchievementsByCategory(String category);
}

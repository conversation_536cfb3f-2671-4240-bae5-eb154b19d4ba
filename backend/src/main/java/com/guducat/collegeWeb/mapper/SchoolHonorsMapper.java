package com.guducat.collegeWeb.mapper;

import com.guducat.collegeWeb.entity.SchoolHonors;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 学校荣誉数据访问接口
 */
@Mapper
public interface SchoolHonorsMapper {

    /**
     * 获取所有启用的荣誉数据
     * @return 荣誉数据列表
     */
    @Results(id = "schoolHonorsResultMap", value = {
        @Result(property = "id", column = "id", id = true),
        @Result(property = "honorName", column = "honor_name"),
        @Result(property = "issuer", column = "issuer"),
        @Result(property = "honorYear", column = "honor_year"),
        @Result(property = "honorCategory", column = "honor_category"),
        @Result(property = "description", column = "description"),
        @Result(property = "displayOrder", column = "display_order"),
        @Result(property = "isActive", column = "is_active"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    @Select("SELECT * FROM school_honors WHERE is_active = 1 ORDER BY display_order")
    List<SchoolHonors> getAllActiveHonors();

    /**
     * 根据荣誉级别获取荣誉数据
     * @param category 荣誉级别
     * @return 荣誉数据列表
     */
    @ResultMap("schoolHonorsResultMap")
    @Select("SELECT * FROM school_honors WHERE is_active = 1 AND honor_category = #{category} ORDER BY display_order")
    List<SchoolHonors> getHonorsByCategory(String category);

}

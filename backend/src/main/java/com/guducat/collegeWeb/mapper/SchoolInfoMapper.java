package com.guducat.collegeWeb.mapper;

import com.guducat.collegeWeb.entity.SchoolInfo;
import org.apache.ibatis.annotations.*;

/**
 * 学校基本信息数据访问接口
 */
@Mapper
public interface SchoolInfoMapper {

    /**
     * 获取学校基本信息
     * @return 学校基本信息
     */
    @Results(id = "schoolInfoResultMap", value = {
        @Result(property = "id", column = "id", id = true),
        @Result(property = "schoolName", column = "school_name"),
        @Result(property = "schoolCode", column = "school_code"),
        @Result(property = "schoolIntroduction", column = "school_introduction"),
        @Result(property = "schoolMission", column = "school_mission"),
        @Result(property = "schoolPhilosophy", column = "school_philosophy"),
        @Result(property = "trainingGoals", column = "training_goals"),
        @Result(property = "bannerImage", column = "banner_image"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    @Select("SELECT * FROM school_info LIMIT 1")
    SchoolInfo getSchoolInfo();

}

package com.guducat.collegeWeb.mapper;

import com.guducat.collegeWeb.entity.SchoolStatistics;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 学校统计数据访问接口
 */
@Mapper
public interface SchoolStatisticsMapper {

    /**
     * 获取所有启用的统计数据
     * @return 统计数据列表
     */
    @Results(id = "schoolStatisticsResultMap", value = {
        @Result(property = "id", column = "id", id = true),
        @Result(property = "statName", column = "stat_name"),
        @Result(property = "statValue", column = "stat_value"),
        @Result(property = "statUnit", column = "stat_unit"),
        @Result(property = "displayOrder", column = "display_order"),
        @Result(property = "isActive", column = "is_active"),
        @Result(property = "createdAt", column = "created_at"),
        @Result(property = "updatedAt", column = "updated_at")
    })
    @Select("SELECT * FROM school_statistics WHERE is_active = 1 ORDER BY display_order")
    List<SchoolStatistics> getAllActiveStatistics();

}

package com.guducat.collegeWeb.service;

import com.guducat.collegeWeb.entity.SchoolInfo;
import com.guducat.collegeWeb.entity.SchoolStatistics;
import com.guducat.collegeWeb.entity.SchoolHonors;
import com.guducat.collegeWeb.entity.SchoolAchievements;
import com.guducat.collegeWeb.mapper.SchoolInfoMapper;
import com.guducat.collegeWeb.mapper.SchoolStatisticsMapper;
import com.guducat.collegeWeb.mapper.SchoolHonorsMapper;
import com.guducat.collegeWeb.mapper.SchoolAchievementsMapper;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 学校信息服务类
 */
@Service
public class SchoolService {

    private final SchoolInfoMapper schoolInfoMapper;
    private final SchoolStatisticsMapper schoolStatisticsMapper;
    private final SchoolHonorsMapper schoolHonorsMapper;
    private final SchoolAchievementsMapper schoolAchievementsMapper;

    public SchoolService(SchoolInfoMapper schoolInfoMapper,
                        SchoolStatisticsMapper schoolStatisticsMapper,
                        SchoolHonorsMapper schoolHonorsMapper,
                        SchoolAchievementsMapper schoolAchievementsMapper) {
        this.schoolInfoMapper = schoolInfoMapper;
        this.schoolStatisticsMapper = schoolStatisticsMapper;
        this.schoolHonorsMapper = schoolHonorsMapper;
        this.schoolAchievementsMapper = schoolAchievementsMapper;
    }

    /**
     * 获取学校基本信息
     * @return 学校基本信息
     */
    public SchoolInfo getSchoolInfo() {
        return schoolInfoMapper.getSchoolInfo();
    }

    /**
     * 获取学校统计数据
     * @return 统计数据列表
     */
    public List<SchoolStatistics> getSchoolStatistics() {
        return schoolStatisticsMapper.getAllActiveStatistics();
    }

    /**
     * 获取学校荣誉数据
     * @return 荣誉数据列表
     */
    public List<SchoolHonors> getSchoolHonors() {
        return schoolHonorsMapper.getAllActiveHonors();
    }

    /**
     * 根据级别获取学校荣誉数据
     * @param category 荣誉级别
     * @return 荣誉数据列表
     */
    public List<SchoolHonors> getSchoolHonorsByCategory(String category) {
        return schoolHonorsMapper.getHonorsByCategory(category);
    }

    /**
     * 获取学校成就数据
     * @return 成就数据列表
     */
    public List<SchoolAchievements> getSchoolAchievements() {
        return schoolAchievementsMapper.getAllActiveAchievements();
    }

    /**
     * 根据分类获取学校成就数据
     * @param category 成就分类
     * @return 成就数据列表
     */
    public List<SchoolAchievements> getSchoolAchievementsByCategory(String category) {
        return schoolAchievementsMapper.getAchievementsByCategory(category);
    }
}

spring.application.name=collageWeb

#数据库配置项（从环境变量读取）
spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USER}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=${DB_DRIVER}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect

# SpringDoc OpenAPI 配置
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.doc-expansion=none
springdoc.swagger-ui.display-request-duration=true
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.deep-linking=true
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
springdoc.packages-to-scan=com.guducat.collegeWeb.controller
# 解决Spring Boot 3.4.4与SpringDoc兼容性问题
springdoc.cache.disabled=true
springdoc.writer-with-default-pretty-printer=true
springdoc.model-and-view-allowed=true


###更改监控级别
logging.level.org.springframework.web=DEBUG
logging.level.com.guducat.collegeWeb=DEBUG
logging.level.org.mybatis=DEBUG
logging.level.root=INFO
# 设置日志输出格式和编码
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %magenta(%-5level) %green([%-50.50class]) >>> %cyan(%msg) %n
logging.charset.console=UTF-8

#mybatis配置
mybatis.mapper-locations=classpath*:com/guducat/collegeWeb/mapper/*.xml
mybatis.type-aliases-package=com.guducat.collegeWeb.entity
mybatis.configuration.map-underscore-to-camel-case=true

# 完全禁用Spring Security自动配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# HikariCP 连接池配置
# 连接超时时间（毫秒）
spring.datasource.hikari.connection-timeout=30000
# 最小空闲连接数
spring.datasource.hikari.minimum-idle=5
# 最大连接数
spring.datasource.hikari.maximum-pool-size=15
# 空闲连接最大存活时间（毫秒）- 降低以避免空闲连接被MySQL关闭
spring.datasource.hikari.idle-timeout=300000
# 连接最大生存时间（毫秒）- 降低以确保在MySQL超时前主动更新连接
spring.datasource.hikari.max-lifetime=580000
# 连接测试查询
spring.datasource.hikari.connection-test-query=SELECT 1
# 自动提交
spring.datasource.hikari.auto-commit=true
# 连接池名称
spring.datasource.hikari.pool-name=HikariCP-Pool
# 验证超时时间（毫秒）
spring.datasource.hikari.validation-timeout=5000
# 允许连接池暂停
spring.datasource.hikari.allow-pool-suspension=false
# 注册MBeans
spring.datasource.hikari.register-mbeans=true
# 泄漏检测阈值（毫秒）
spring.datasource.hikari.leak-detection-threshold=60000
# 保持连接活跃 - 添加以确保连接不会因为不活动而被关闭
spring.datasource.hikari.keepalive-time=240000

# 端口
# server.port=8081

# 文件上传配置
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true

############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
# token 名称（同时也是 cookie 名称）
sa-token.token-name=satoken
# token 有效期（单位：秒） 默认30天，-1 代表永久有效
sa-token.timeout=-1
# token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
sa-token.active-timeout=-1
# 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
sa-token.is-concurrent=true
# 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
sa-token.is-share=false
# token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
sa-token.token-style=uuid
# 是否输出操作日志
sa-token.is-log=true

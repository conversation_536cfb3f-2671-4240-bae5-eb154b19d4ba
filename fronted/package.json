{"name": "fronted", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@tailwindcss/vite": "^4.0.16", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.9.0", "swiper": "^11.2.6", "vue": "^3.5.14", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "daisyui": "^5.0.27", "postcss": "^8.5.3", "tailwindcss": "^4.0.16", "vite": "^6.3.5", "terser": "^5.40.0"}}
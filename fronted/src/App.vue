<script setup>
import Header from "./components/composables/header.vue";
import Footer from "./components/composables/footer.vue";

//当检测到用户UserAgent为移动设备时，自动跳转到另外一个https://m.college.guducat.cn/ 移动端页面，并在地址栏中添加一个参数，表示来自PC端。（301跳转）
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

onMounted(() => {
  const userAgent = navigator.userAgent;
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

  if (isMobile) {
    //直接301跳转到移动端页面，不带参数，表示来自PC端。
    window.location.href = 'https://m.college.guducat.cn/';
  }
});
</script>

<template>
  <div>
    <!-- 顶部导航栏 -->
    <Header />

    <!-- 动态路由出口 -->
  
    <RouterView />
  

    <!-- 底部导航栏 -->
    <Footer />
  </div>
</template>
<style scoped>

</style>

<template>
  <footer class="bg-white bg-gradient-to-b from-[#C32A31] to-[#991E29]">
    <div class="mx-auto max-w-7xl px-6 pt-16 pb-8 sm:pt-24 lg:px-8 lg:pt-32">
      <div class="xl:grid xl:grid-cols-3 xl:gap-8">
        <img class="h-15" src="/images/NY_Logo_All_Rectangular.png" alt="Company name" />
        <div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
          <div class="md:grid md:grid-cols-2 md:gap-8">
            <div>
              <h3 class="text-sm/6 font-semibold text-gray-900">学校机构</h3>
              <ul role="list" class="mt-6 space-y-4">
                <li v-for="item in navigation.collegeInfo" :key="item.name">
                  <a :href="item.href" class="text-sm/6 text-gray-600 hover:text-gray-900">{{ item.name }}</a>
                </li>
              </ul>
            </div>
            <div class="mt-10 md:mt-0">
              <h3 class="text-sm/6 font-semibold text-gray-900">党群机构</h3>
              <ul role="list" class="mt-6 space-y-4">
                <li v-for="item in navigation.support" :key="item.name">
                  <a :href="item.href" class="text-sm/6 text-gray-600 hover:text-gray-900">{{ item.name }}</a>
                </li>
              </ul>
            </div>
          </div>
          <div class="md:grid md:grid-cols-2 md:gap-8">
            <div>
              <h3 class="text-sm/6 font-semibold text-gray-900">行政教辅机构</h3>
              <ul role="list" class="mt-6 space-y-4">
                <li v-for="item in navigation.company" :key="item.name">
                  <a :href="item.href" class="text-sm/6 text-gray-600 hover:text-gray-900">{{ item.name }}</a>
                </li>
              </ul>
            </div>
            <div class="mt-10 md:mt-0">
              <h3 class="text-sm/6 font-semibold text-gray-900">教辅机构</h3>
              <ul role="list" class="mt-6 space-y-4">
                <li v-for="item in navigation.legal" :key="item.name">
                  <a :href="item.href" class="text-sm/6 text-gray-600 hover:text-gray-900">{{ item.name }}</a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-8 border-t border-gray-900/10 pt-8 md:flex md:items-center md:justify-between">
        <div class="flex gap-x-6 md:order-2">
          <a v-for="item in navigation.social" :key="item.name" :href="item.href" class="text-gray-600 hover:text-gray-800">
            <span class="sr-only">{{ item.name }}</span>
            <component :is="item.icon" class="size-6" aria-hidden="true" />
          </a>
        </div>
        <p class="mt-8 text-sm/6 text-gray-600 md:order-1 md:mt-0">&copy; Guangzhou NanYang Polytechnic All Rights Reserved .</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { defineComponent, h } from 'vue'

const navigation = {
  collegeInfo: [
    { name: '建筑与艺术学院', href: '#' },
    { name: '数字商学院', href: '#' },
    { name: '智能工程学院', href: '#' },
    { name: '人工智能学院', href: '#' },
    { name: '人文与教育学院', href: '#' },
    { name: '马克思主义学院', href: '#' },
    { name: '卫生健康学院', href: '#' },
  ],
  support: [
    { name: '党群工作办公室', href: '#' },
    { name: '组织部', href: '#' },
    { name: '统战部', href: '#' },
    { name: '宣传部', href: '#' },
    { name: '工会', href: '#' },
    { name: '关工委', href: '#' },
    { name: '团委', href: '#' },
  ],
  company: [
    { name: '教学科研部', href: '#' },
    { name: '人力资源部', href: '#' },
    { name: '学生发展部', href: '#' },
    { name: '合作交流部', href: '#' },
    { name: '招生办公室', href: '#' },
  ],
  legal: [
    { name: '图书馆', href: '#' },
    { name: '财务部', href: '#' },
    { name: '教育技术与网络中心', href: '#' },
  ],
  social: [
    {
      name: 'WeChat',
      href: '#',
      icon: defineComponent({
        render: () =>
          h('svg', { 
            fill: 'currentColor', 
            viewBox: '0 0 1284 1024' 
          }, [
            h('path', {
              d: 'M558.**********.972943c0 165.**********.**********.**********.**********.003382 48.487035 0 95.819617-8.**********.379932-21.934611 11.544532 5.772266 94.665163 77.**********.136415 64.64938 12.698985-10.390079-30.015784-87.738444-33.**********.591883 87.738444-54.**********.**********.**********.**********.126268 0-165.**********.**********.**********.498309-299.003383S558.755355 514.886133 558.**********.972943z m438.692221-105.055243c0-28.86133 23.089064-51.950395 51.950395-51.950394s51.950395 23.089064 51.950394 51.950394-23.089064 51.950395-51.950394 51.950395-51.950395-23.089064-51.950395-51.950395z m-255.13416 0c0-28.86133 23.089064-51.950395 51.950395-51.950394s51.950395 23.089064 51.950394 51.950394-23.089064 51.950395-51.950394 51.950395-51.950395-23.089064-51.950395-51.950395z'
            }),
            h('path', {
              d: 'M526.430665 677.664036c0-180.094701 177.785795-325.555806 395.977452-325.555806 11.544532 0 23.089064 0 33.479143 1.154453C931.643743 154.696731 727.305524 0 479.098083 0 214.728298 0 0 176.631342 0 394.822999c0 129.29876 76.193912 244.744081 192.793687 316.32018-4.617813 17.316798-60.031567 120.063134-43.869223 133.916573 24.243517 16.162345 133.916573-78.502818 148.924465-85.429538 55.413754 18.471251 116.599775 28.86133 181.249154 28.861331 23.089064 0 46.178129-1.154453 69.267193-4.617813-13.853439-32.32469-21.934611-68.11274-21.934611-106.209696zM646.493799 184.712514c38.096956 0 69.267193 31.170237 69.267193 69.267193 1.154453 38.096956-30.015784 69.267193-69.267193 69.267193-38.096956 0-69.267193-31.170237-69.267192-69.267193s31.170237-69.267193 69.267192-69.267193zM310.547914 323.2469c-38.096956 0-69.267193-31.170237-69.267192-69.267193s31.170237-69.267193 69.267192-69.267193 69.267193 31.170237 69.267193 69.267193-31.170237 69.267193-69.267193 69.267193z'
            })
          ])
      }),
    },
    {
      name: 'QQ',
      href: '#',
      icon: defineComponent({
        render: () =>
          h('svg', { 
            fill: 'currentColor', 
            viewBox: '0 0 1024 1024' 
          }, [
            h('path', {
              d: 'M824.8 613.2c-16-51.4-34.4-94.6-62.7-165.3C766.5 262.2 689.3 112 511.5 112 331.7 112 256.2 265.2 261 447.9c-28.4 70.8-46.7 113.7-62.7 165.3-34 109.5-23 154.8-14.6 155.8 18 2.2 70.1-82.4 70.1-82.4 0 49 25.2 112.9 79.8 159-26.4 8.1-85.7 29.9-71.6 53.8 11.4 19.3 196.2 12.3 249.5 6.3 53.3 6 238.1 13 249.5-6.3 14.1-23.8-45.3-45.7-71.6-53.8 54.6-46.2 79.8-110.1 79.8-159 0 0 52.1 84.6 70.1 82.4 8.5-1.1 19.5-46.4-14.5-155.8z'
            })
          ])
      }),
    },
    {
      name: 'Bilibili',
      href: '#',
      icon: defineComponent({
        render: () =>
          h('svg', { 
            fill: 'currentColor', 
            viewBox: '0 0 1024 1024' 
          }, [
            h('path', {
              d: 'M1019.54782609 345.3106087c-3.20556522-142.1133913-127.15408696-169.36069565-127.15408696-169.36069566s-96.70121739-0.53426087-222.25252174-1.60278261l91.3586087-88.15304347s14.42504348-18.16486957-10.15095652-38.46678261c-24.576-20.30191304-26.17878261-11.21947826-34.72695653-5.87686957-7.47965217 5.3426087-117.00313043 112.72904348-136.23652174 131.96243479-49.68626087 0-101.50956522-0.53426087-151.73008695-0.53426087h17.63060869S315.392 43.98747826 306.84382609 38.1106087s-9.61669565-14.42504348-34.72695652 5.87686956c-24.576 20.30191304-10.15095652 38.46678261-10.15095653 38.46678261l93.49565218 90.82434783c-101.50956522 0-189.12834783 0.53426087-229.73217392 2.13704347C-5.69878261 213.34817391 4.45217391 345.3106087 4.45217391 345.3106087s1.60278261 283.15826087 0 426.34017391c14.42504348 143.18191304 124.48278261 166.15513043 124.48278261 166.15513043s43.8093913 1.06852174 76.39930435 1.06852174c3.20556522 9.08243478 5.87686957 53.96034783 56.0973913 53.96034783 49.68626087 0 56.0973913-53.96034783 56.09739131-53.96034783s365.96869565-1.60278261 396.42156522-1.60278261c1.60278261 15.49356522 9.08243478 56.63165217 59.30295652 56.09739131 49.68626087-1.06852174 53.42608696-59.30295652 53.42608695-59.30295652s17.09634783-1.60278261 67.85113044 0c118.60591304-21.90469565 125.55130435-160.81252174 125.55130435-160.81252174s-2.13704348-285.82956522-0.53426087-427.94295652z m-102.04382609 453.05321739c0 22.43895652-17.6306087 40.60382609-39.53530435 40.60382608h-721.25217391c-21.90469565 0-39.53530435-18.16486957-39.53530435-40.60382608V320.20034783c0-22.43895652 17.6306087-40.60382609 39.53530435-40.60382609h721.25217391c21.90469565 0 39.53530435 18.16486957 39.53530435 40.60382609v478.16347826z'
            }),
            h('path', {
              d: 'M409.088 418.816l-203.264 38.912 17.408 76.288 201.216-38.912zM518.656 621.056c-49.664 106.496-94.208 26.112-94.208 26.112l-33.28 21.504s65.536 89.6 128 21.504c73.728 68.096 130.048-22.016 130.048-22.016l-30.208-19.456c0-0.512-52.736 75.776-100.352-27.648zM619.008 495.104l201.728 38.912 16.896-76.288-202.752-38.912z'
            })
          ])
      })
    }
  ],
}
</script>

<style scoped>
  *{
    color: #fff;
  }
</style>

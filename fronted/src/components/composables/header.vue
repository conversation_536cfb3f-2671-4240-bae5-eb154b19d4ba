<template>
  <header class="relative isolate z-[9999] bg-white bg-gradient-to-t from-[#C32A31] to-[#991E29]">
    <nav class="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8" aria-label="全局导航">
      <div class="flex lg:flex-1">
        <a href="/" class="-m-1.5 p-1.5 flex items-center space-x-3">
          <span class="sr-only">Guangzhou Nanyang Polytechnic College</span>
          <img class="h-12 w-auto" src="/images/NY_RectangularLogo.png" alt="" />
          <div class="hidden sm:flex flex-col justify-center">
            <div class="text-white font-black text-2xl leading-tight tracking-wide" style="font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;">
              广州南洋理工职业学院
            </div>
            <div class="text-white/70 font-medium text-xs leading-tight uppercase whitespace-nowrap tracking-tight" style="font-family: 'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif;">
              GUANGZHOU NANYANG POLYTECHNIC COLLEGE
            </div>
          </div>
        </a>
      </div>
      <div class="flex lg:hidden">
        <button type="button" class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-white" @click="mobileMenuOpen = true">
          <span class="sr-only">展开导航菜单</span>
          <Bars3Icon class="size-6" aria-hidden="true" />
        </button>
      </div>
      <PopoverGroup class="hidden lg:flex lg:gap-x-12">
        <!--学校详情-->
        <Popover>
          <PopoverButton class="flex items-center gap-x-1 text-sm/6 font-semibold leading-6 text-white">
            学校详情
            <ChevronDownIcon class="h-5 w-5 flex-none text-white inline-block" aria-hidden="true" />
          </PopoverButton>

          <transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0 -translate-y-1" enter-to-class="opacity-100 translate-y-0" leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 -translate-y-1">
            <PopoverPanel class="absolute inset-x-0 top-0 z-[9998] bg-gradient-to-t from-[#C32A31] to-[#991E29] pt-14 ring-1 shadow-lg ring-gray-900/5">
              <div class="mx-auto grid max-w-7xl grid-cols-4 gap-x-4 px-6 py-10 lg:px-8 xl:gap-x-8">
                <div v-for="item in collegeInfo" :key="item.name" class="group relative rounded-lg p-6 text-sm leading-6 hover:bg-[#f59e0b]">
                  <div class="flex h-11 w-11 items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                    <component :is="item.icon" class="h-6 w-6 text-gray-600 group-hover:text-indigo-600" aria-hidden="true" />
                  </div>
                  <router-link v-if="item.route" :to="item.route" class="mt-6 block font-semibold text-white">
                    {{ item.name }}
                    <span class="absolute inset-0" />
                  </router-link>
                  <a v-else :href="item.href" class="mt-6 block font-semibold text-white">
                    {{ item.name }}
                    <span class="absolute inset-0" />
                  </a>
                  <p class="mt-1 text-white">{{ item.description }}</p>
                </div>
              </div>
              <div class="bg-gradient-to-b from-[#C32A31] to-[#991E29]">
                <div class="mx-auto max-w-7xl px-6 lg:px-8 ">
                  <div class="grid grid-cols-3 divide-x divide-gray-900/5 border-x border-gray-900/5">
                    <a v-for="item in callsToAction" :key="item.name" :href="item.href" class="flex items-center justify-center gap-x-2.5 p-3 text-sm/6 font-semibold text-white ">
                      <component :is="item.icon" class="h-5 w-5 flex-none text-white" aria-hidden="true" />
                      {{ item.name }}
                    </a>
                  </div>
                </div>
              </div>
            </PopoverPanel>
          </transition>

        </Popover>
        <!--学校详情2-->
        <Popover>
          <PopoverButton class="flex items-center gap-x-1 text-sm/6 font-semibold leading-6 text-white">
            功能
            <ChevronDownIcon class="h-5 w-5 flex-none text-white inline-block" aria-hidden="true" />
          </PopoverButton>
          <transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0 -translate-y-1" enter-to-class="opacity-100 translate-y-0" leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 -translate-y-1">
            <PopoverPanel class="absolute inset-x-0 top-0 z-[9998] bg-gradient-to-t from-[#C32A31] to-[#991E29] pt-14 ring-1 shadow-lg ring-gray-900/5">
              <div class="mx-auto grid max-w-7xl grid-cols-4 gap-x-4 px-6 py-10 lg:px-8 xl:gap-x-8">
                <div v-for="item in functionInfo" :key="item.name" class="group relative rounded-lg p-6 text-sm leading-6 hover:bg-orange-400">
                  <div class="flex h-11 w-11 items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                    <component :is="item.icon" class="h-6 w-6 text-gray-600 group-hover:text-indigo-600" aria-hidden="true" />
                  </div>
                  <a :href="item.href" class="mt-6 block font-semibold text-white">
                    {{ item.name }}
                    <span class="absolute inset-0" />
                  </a>
                  <p class="mt-1 text-white">{{ item.description }}</p>
                </div>
              </div>
            </PopoverPanel>
          </transition>
        </Popover>
        <!--学校详情3-->
        <!--就业信息-->
        <Popover class="relative">
          <PopoverButton class="flex items-center gap-x-1 text-sm/6 font-semibold text-white">
            学生就业
            <ChevronDownIcon class="size-5 flex-none text-white inline-block" aria-hidden="true" />
          </PopoverButton>
          <transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0 translate-y-1" enter-to-class="opacity-100 translate-y-0" leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 translate-y-1">
            <PopoverPanel class="absolute top-full -left-8 z-[9998] mt-3 w-screen max-w-md overflow-hidden rounded-3xl bg-gradient-to-b from-[#C32A31] to-[#991E29] ring-1 shadow-lg ring-gray-900/5">
              <div class="p-4">
                <div v-for="item in marketInfo" :key="item.name" class="group relative flex gap-x-6 rounded-lg p-4 text-sm/6 hover:bg-[#f59e0b]">
                  <div class="mt-1 flex size-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                    <component :is="item.icon" class="size-6 text-gray-600 group-hover:text-indigo-600" aria-hidden="true" />
                  </div>
                  <div class="flex-auto">
                    <a :href="item.href" class="block font-semibold text-white">
                      {{ item.name }}
                      <span class="absolute inset-0" />
                    </a>
                    <p class="mt-1 text-gray-200">{{ item.description }}</p>
                  </div>
                </div>
              </div>
            </PopoverPanel>
          </transition>
        </Popover>
        <!--学院详情-学院一览-->
        <Popover class="relative">
          <PopoverButton class="flex items-center gap-x-1 text-sm/6 font-semibold text-white">
            学院一览
            <ChevronDownIcon class="size-5 flex-none text-white inline-block" aria-hidden="true" />
          </PopoverButton>

          <transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0 translate-y-1" enter-to-class="opacity-100 translate-y-0" leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 translate-y-1">
            <PopoverPanel class="absolute top-full -left-8 z-[9998] mt-3 w-screen max-w-md overflow-hidden rounded-3xl bg-gradient-to-b from-[#C32A31] to-[#991E29] ring-1 shadow-lg ring-gray-900/5">
              <div class="p-4">
                <div v-for="item in SchoolInfo" :key="item.name" class="group relative flex gap-x-6 rounded-lg p-4 text-sm/6 hover:bg-orange-400">
                  <div class="mt-1 flex size-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-white">
                    <component :is="item.icon" class="size-6 text-gray-600 group-hover:text-indigo-600" aria-hidden="true" />
                  </div>
                  <div class="flex-auto">
                    <a :href="item.href" class="block font-semibold text-white">
                      {{ item.name }}
                      <span class="absolute inset-0" />
                    </a>
                    <p class="mt-1 text-gray-600">{{ item.description }}</p>
                  </div>
                </div>
              </div>
            </PopoverPanel>
          </transition>
        </Popover>
      </PopoverGroup>
      <div class="hidden lg:flex lg:flex-1 lg:justify-end">
        <!-- 未登录状态显示登录按钮 -->
        <router-link v-if="!isLoggedIn" to="/login" class="text-sm/6 font-semibold text-white flex items-center">
          登录<span aria-hidden="true" class="ml-1 inline-block">&rarr;</span>
        </router-link>

        <!-- 已登录状态显示用户信息和下拉菜单 -->
        <div v-else class="relative">
          <Popover class="relative">
            <PopoverButton class="flex items-center text-sm/6 font-semibold text-white">
              <span class="mr-2">{{ displayName }}</span>
              <ChevronDownIcon class="h-5 w-5" aria-hidden="true" />
            </PopoverButton>

            <transition
              enter-active-class="transition ease-out duration-200"
              enter-from-class="opacity-0 translate-y-1"
              enter-to-class="opacity-100 translate-y-0"
              leave-active-class="transition ease-in duration-150"
              leave-from-class="opacity-100 translate-y-0"
              leave-to-class="opacity-0 translate-y-1"
            >
              <PopoverPanel class="absolute right-0 z-[9998] mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <!-- 管理员面板入口 - 只有具有有效管理权限的用户才能看到 -->
                <router-link
                  v-if="userInfo.permissions && userInfo.permissions.length > 0 &&
                        !userInfo.permissions.every(perm => perm === '学生' || perm === '教师')"
                  to="/admin"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  管理员面板
                </router-link>

                <!-- 个人信息 -->
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  个人信息
                </a>

                <!-- 登出按钮 -->
                <button @click="handleLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  退出登录
                </button>
              </PopoverPanel>
            </transition>
          </Popover>
        </div>
      </div>
    </nav>

    <!--移动端样式(上-下侧边栏)-->
    <Dialog class="lg:hidden" @close="mobileMenuOpen = false" :open="mobileMenuOpen">
      <div class="fixed inset-0 z-[9998]" />
      <DialogPanel class="fixed inset-y-0 right-0 z-[9999] w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10 bg-gradient-to-t from-[#C32A31] to-[#991E29]">
        <div class="flex items-center justify-between">
          <a href="#" class="-m-1.5 p-1.5 flex items-center space-x-2">
            <span class="sr-only">Guangzhou Nanyang Polytechnic College</span>
            <img class="h-10 w-auto" src="/images/NY_Logo_All_Rectangular.png" alt="" />
            <div class="flex flex-col justify-center">
              <div class="text-white font-black text-xl leading-tight tracking-wide" style="font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;">
                广州南洋理工职业学院
              </div>
              <div class="text-white/80 font-medium text-[10px] leading-tight uppercase whitespace-nowrap tracking-tight" style="font-family: 'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif;">
                GUANGZHOU NANYANG POLYTECHNIC COLLEGE
              </div>
            </div>
          </a>
          <button type="button" class="-m-2.5 rounded-md p-2.5 text-white" @click="mobileMenuOpen = false">
            <span class="sr-only">关闭菜单</span>
            <XMarkIcon class="size-6" aria-hidden="true" />
          </button>
        </div>
        <div class="mt-6 flow-root">
          <div class="-my-6 divide-y divide-gray-500/10">
            <div class="space-y-2 py-6">
              <!--移动端学校详情1-->
              <Disclosure as="div" class="-mx-3" v-slot="{ open }">
                <DisclosureButton class="flex w-full items-center justify-between rounded-lg py-2 pr-3.5 pl-3 text-base/7 font-semibold text-leading-7 text-white hover:bg-orange-400">
                  学校详情
                  <ChevronDownIcon :class="[open ? 'rotate-180' : '', 'size-5 flex-none inline-block']" aria-hidden="true" />
                </DisclosureButton>
                <DisclosurePanel class="mt-2 space-y-2">
                  <DisclosureButton v-for="item in collegeInfo" :key="item.name"
                    :as="item.route ? 'router-link' : 'a'"
                    :to="item.route ? item.route : undefined"
                    :href="item.route ? undefined : item.href"
                    @click="closeMobileMenu"
                    class="block rounded-lg py-2 pr-3 pl-6 text-sm/6 font-semibold leading-7 text-white hover:bg-orange-400">
                    {{ item.name }}
                  </DisclosureButton>
                </DisclosurePanel>
              </Disclosure>
              <!--移动端学校详情2-->
              <Disclosure as="div" class="-mx-3" v-slot="{ open }">
                <DisclosureButton class="flex w-full items-center justify-between rounded-lg py-2 pr-3.5 pl-3 text-base/7 font-semibold text-leading-7 text-white hover:bg-orange-400">
                  功能
                  <ChevronDownIcon :class="[open ? 'rotate-180' : '', 'size-5 flex-none inline-block']" aria-hidden="true" />
                </DisclosureButton>
                <DisclosurePanel class="mt-2 space-y-2">
                  <DisclosureButton v-for="item in functionInfo" :key="item.name" as="a" :href="item.href" @click="closeMobileMenu" class="block rounded-lg py-2 pr-3 pl-6 text-sm/6 font-semibold leading-7 text-white hover:bg-orange-400">
                    {{ item.name }}
                  </DisclosureButton>
                </DisclosurePanel>
              </Disclosure>
              <!--移动端学校详情3-->
              <Disclosure as="div" class="-mx-3" v-slot="{ open }">
                <DisclosureButton class="flex w-full items-center justify-between rounded-lg py-2 pr-3.5 pl-3 text-base/7 font-semibold text-leading-7 text-white hover:bg-orange-400">
                  市场
                  <ChevronDownIcon :class="[open ? 'rotate-180' : '', 'h-5 w-5 flex-none inline-block']" aria-hidden="true" />
                </DisclosureButton>
                <DisclosurePanel class="mt-2 space-y-2">
                  <DisclosureButton v-for="item in marketInfo" :key="item.name" as="a" :href="item.href" @click="closeMobileMenu" class="block rounded-lg py-2 pr-3 pl-6 text-sm/6 font-semibold leading-7 text-white hover:bg-orange-400">
                    {{ item.name }}
                  </DisclosureButton>
                </DisclosurePanel>
              </Disclosure>
              <!--移动端学院详情-学院一览-->
              <Disclosure as="div" class="-mx-3" v-slot="{ open }">
                <DisclosureButton class="flex w-full items-center justify-between rounded-lg py-2 pr-3.5 pl-3 text-base/7 font-semibold text-leading-7 text-white hover:bg-orange-400">
                  学院一览
                  <ChevronDownIcon :class="[open ? 'rotate-180' : '', 'h-5 w-5 flex-none inline-block']" aria-hidden="true" />
                </DisclosureButton>
                <DisclosurePanel class="mt-2 space-y-2">
                  <DisclosureButton v-for="item in SchoolInfo" :key="item.name" as="a" :href="item.href" @click="closeMobileMenu" class="block rounded-lg py-2 pr-3 pl-6 text-sm/6 font-semibold leading-7 text-white hover:bg-orange-400">
                    {{ item.name }}
                  </DisclosureButton>
                </DisclosurePanel>
              </Disclosure>

            </div>
            <div class="py-6">
              <!-- 未登录状态显示登录按钮 -->
              <router-link v-if="!isLoggedIn" to="/login" @click="closeMobileMenu" class="-mx-3 flex items-center rounded-lg px-3 py-2.5 text-base/7 font-semibold leading-7 text-white hover:bg-orange-400">
                登录<span aria-hidden="true" class="ml-1 inline-block">&rarr;</span>
              </router-link>

              <!-- 已登录状态显示用户信息和操作 -->
              <div v-else class="space-y-2">
                <div class="-mx-3 flex items-center rounded-lg px-3 py-2.5 text-base/7 font-semibold leading-7 text-white">
                  {{ displayName }}
                </div>

                <!-- 管理员面板入口 - 只有具有有效管理权限的用户才能看到 -->
                <router-link
                  v-if="userInfo.permissions && userInfo.permissions.length > 0 &&
                        !userInfo.permissions.every(perm => perm === '学生' || perm === '教师')"
                  to="/admin"
                  @click="closeMobileMenu"
                  class="-mx-3 flex items-center rounded-lg px-3 py-2.5 text-base/7 font-semibold leading-7 text-white hover:bg-orange-400">
                  管理员面板
                </router-link>

                <!-- 个人信息 -->
                <a href="#" @click="closeMobileMenu" class="-mx-3 flex items-center rounded-lg px-3 py-2.5 text-base/7 font-semibold leading-7 text-white hover:bg-orange-400">
                  个人信息
                </a>

                <!-- 登出按钮 -->
                <button @click="handleLogout" class="-mx-3 w-full text-left flex items-center rounded-lg px-3 py-2.5 text-base/7 font-semibold leading-7 text-white hover:bg-orange-400">
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </DialogPanel>
    </Dialog>
  </header>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import request from '../../utils/request'
import eventBus from '../../utils/eventBus'
import {
  Dialog,
  DialogPanel,
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Popover,
  PopoverButton,
  PopoverGroup,
  PopoverPanel,
} from '@headlessui/vue'
import {
  Bars3Icon,
  ChartPieIcon,
  CursorArrowRaysIcon,
  FingerPrintIcon,
  SquaresPlusIcon,
  XMarkIcon,
  BuildingOffice2Icon,
  UsersIcon,
  GlobeAltIcon,
  AcademicCapIcon, CpuChipIcon, CommandLineIcon, BriefcaseIcon
} from '@heroicons/vue/24/outline'
import { ChevronDownIcon, PhoneIcon, PlayCircleIcon, RectangleGroupIcon } from '@heroicons/vue/20/solid'

//学校详情
const collegeInfo = [
  {
    name: '学校简介',
    description: '简单了解南洋',
    route: { name: 'SchoolIntroduction' },
    icon: ChartPieIcon,
  },
  {
    name: '学校荣誉',
    description: '学校获得的各类荣誉',
    route: { name: 'SchoolHonors' },
    icon: CursorArrowRaysIcon,
  },
  { name: '标志成果',
    description: '学校办学成果展示',
    route: { name: 'SignificantAchievements' },
    icon: FingerPrintIcon,
  },
  {
    name: '组织架构',
    description: '学校管理体系结构',
    route: { name: 'OrganizationStructure' },
    icon: SquaresPlusIcon,
  },
]
// const callsToAction = [
//   { name: '观看演示', href: '#', icon: PlayCircleIcon },
//   { name: '联系销售', href: '#', icon: PhoneIcon },
//   { name: '查看所有产品', href: '#', icon: RectangleGroupIcon },
// ]

//学院详情
const SchoolInfo = [
  {
    name: '人工智能学院',
    description: ' ',
    href: '#',
    icon: CommandLineIcon,
  },
  {
    name: '建筑与艺术学院',
    description: ' ',
    href: '#',
    icon: BuildingOffice2Icon,
  },
  {
    name: '智能工程学院',
    description: ' ',
    href: '#',
    icon: CpuChipIcon,
  },
  {
    name: '人文与教育学院',
    description: ' ',
    href: '#',
    icon: AcademicCapIcon
  },
  {
    name: '数字商学院',
    description: ' ',
    href: '#',
    icon: BriefcaseIcon,
  },
  {
    name: '卫生健康学院',
    description: ' ',
    href: '#',
    icon: UsersIcon,
  }
];

const router = useRouter()
const mobileMenuOpen = ref(false)

// 用户信息和登录状态
const isLoggedIn = ref(false)
const userInfo = ref({
  username: '',
  nickname: '',
  avatar: '',
  isAdmin: false,
  permissions: []
})

// 检查用户是否已登录
const checkLoginStatus = async () => {
  try {
    // 从localStorage获取token
    const token = localStorage.getItem('admin_token')
    if (!token) {
      isLoggedIn.value = false
      return
    }

    // 检查登录状态
    const loginResult = await request.get('/api/user/isLogin')
    if (loginResult.code === 200 && loginResult.data === true) {
      isLoggedIn.value = true

      // 获取用户信息
      const userResult = await request.get('/api/user/info')
      if (userResult.code === 200) {
        const userData = userResult.data
        userInfo.value = {
          username: userData.username || '',
          nickname: userData.nickname || '',
          avatar: userData.avatar || '',
          permissions: userData.permissions || []
        }

        // 获取用户权限信息
        const permissionResult = await request.get('/api/user/permission')
        if (permissionResult.code === 200) {
          userInfo.value.permissions = permissionResult.data.permissions || []
          userInfo.value.isAdmin = permissionResult.data.isAdmin || false
        }
      }
    } else {
      isLoggedIn.value = false
      // 清除无效的token
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    isLoggedIn.value = false
  }
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// 用户登出
const handleLogout = async () => {
  try {
    await request.post('/api/user/logout')
    // 清除本地存储的token和用户信息
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
    isLoggedIn.value = false
    userInfo.value = {
      username: '',
      nickname: '',
      avatar: '',
      isAdmin: false,
      permissions: []
    }
    // 触发登出事件，确保所有组件都知道用户已登出
    eventBus.emit('user-logout')
    // 关闭移动端菜单
    closeMobileMenu()
    // 跳转到首页
    router.push('/')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 显示的用户名
const displayName = computed(() => {
  return userInfo.value.nickname || userInfo.value.username || '用户'
})

// 监听登录事件
const handleLoginEvent = () => {
  console.log('Header received login event, refreshing user info')
  checkLoginStatus()
}

// 监听登出事件
const handleLogoutEvent = () => {
  console.log('Header received logout event, updating user state')
  isLoggedIn.value = false
  userInfo.value = {
    username: '',
    nickname: '',
    avatar: '',
    isAdmin: false,
    permissions: []
  }
}

// 页面加载时检查登录状态并添加事件监听
onMounted(() => {
  checkLoginStatus()
  // 添加登录和登出事件监听
  eventBus.on('user-login', handleLoginEvent)
  eventBus.on('user-logout', handleLogoutEvent)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off('user-login', handleLoginEvent)
  eventBus.off('user-logout', handleLogoutEvent)
})
//功能详情
// 在 collegeInfo 后面添加
const functionInfo = [
  {
    name: '导航',
    description: '校园地图导航',
    href: '#',
    icon: GlobeAltIcon,
  },
  {
    name: '课程表',
    description: '查看课程安排',
    href: '#',
    icon: RectangleGroupIcon,
  },
  {
    name: '成绩查询',
    description: '查看学习成果',
    href: '#',
    icon: ChartPieIcon,
  },
  {
    name: '图书馆',
    description: '在线图书资源',
    href: '#',
    icon: AcademicCapIcon,
  },
]
//市场详情
// 在 SchoolInfo 后面添加
const marketInfo = [
  {
    name: '就业信息',
    description: '最新就业岗位信息',
    href: '#',
    icon: BriefcaseIcon,
  },
  {
    name: '实习机会',
    description: '校企合作实习岗位',
    href: '#',
    icon: BuildingOffice2Icon,
  },
  {
    name: '创业项目',
    description: '创新创业扶持计划',
    href: '#',
    icon: CursorArrowRaysIcon,
  },
  {
    name: '校企合作',
    description: '产学研合作项目',
    href: '#',
    icon: UsersIcon,
  }
];
</script>
<style scoped>
</style>
<template>
    <main class="mx-auto flex w-full max-w-7xl flex-auto flex-col justify-center px-6 py-24 sm:py-64 lg:px-8">
      <p class="text-base/8 font-semibold text-indigo-600">{{ errorCode }}</p>
      <h1 class="mt-4 text-5xl font-semibold tracking-tight text-pretty text-gray-900 sm:text-6xl">{{ errorTitle }}</h1>
      <p class="mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8">{{ errorMessage }}</p>
      <div class="mt-10">
        <RouterLink to="/" class="text-sm/7 font-semibold text-indigo-600 hover:text-indigo-500">
          <span aria-hidden="true">&larr;</span>返回首页
        </RouterLink>
      </div>
    </main>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 根据路由参数或查询参数确定错误类型
const errorCode = computed(() => {
  if (route.query.type === 'api') {
    return '502 Bad Gateway'
  } else if (route.name === 'NotFound') {
    return '404 Not Found'
  }
  return '502 Bad Gateway'
})

const errorTitle = computed(() => {
  if (route.query.type === 'api') {
    return '服务器走丢啦'
  } else if (route.name === 'NotFound') {
    return '页面不存在'
  }
  return '服务器走丢啦'
})

const errorMessage = computed(() => {
  if (route.query.type === 'api') {
    return '抱歉，服务器暂时无法处理您的请求。请稍后再试或联系管理员。'
  } else if (route.name === 'NotFound') {
    return '抱歉，您访问的页面不存在。请检查网址是否正确。'
  }
  return '抱歉，服务器暂时无法处理您的请求。'
})
</script>

<style scoped>

</style>
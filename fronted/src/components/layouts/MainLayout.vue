<template>
  <main class="flex-1 bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <Disclosure as="nav" class="bg-[#C32A31] shadow" v-slot="{ open }">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 南洋学院标题和导航链接的容器 -->
        <div class="flex justify-between h-16">
          <div class="flex pl-0">
            <div class="flex-shrink-0 flex items-center">
              <span class="text-xl font-bold text-white mr-1">南洋学院</span>
            </div>
            <!-- 在移动端视图中隐藏导航链接，使用移动端菜单替代 -->
            <div class="hidden sm:flex space-x-6 ml-9">
              <!-- 导航链接 -->
              <RouterLink :to="{ name: 'HomePage' }" class="border-transparent text-white hover:border-gray-300 hover:text-[#f59e0b] inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                首页
              </RouterLink>
              <RouterLink :to="{ name: 'NewsList' }" class="border-transparent text-white hover:border-gray-300 hover:text-[#f59e0b] inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                最新要闻
              </RouterLink>
            </div>
          </div>
          <div class="-mr-2 flex items-center sm:hidden">
            <!-- 移动端菜单按钮 -->
            <DisclosureButton class="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-gray-200 hover:bg-[#991E29] focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
              <span class="sr-only">打开主菜单</span>
              <Bars3Icon v-if="!open" class="block h-6 w-6" aria-hidden="true" />
              <XMarkIcon v-else class="block h-6 w-6" aria-hidden="true" />
            </DisclosureButton>
          </div>
        </div>
      </div>

      <!-- 移动端菜单 -->
      <DisclosurePanel class="sm:hidden">
        <div class="pt-2 pb-3 space-y-1 max-w-7xl mx-auto" style="padding-left: 4px;">
          <!-- 移动端菜单项，与南洋学院标题对齐 -->
          <RouterLink :to="{ name: 'HomePage' }" class="bg-[#991E29] text-white block pl-3 pr-4 py-2 border-l-4 border-white text-base font-medium" @click="open = false">
            首页
          </RouterLink>
          <RouterLink :to="{ name: 'NewsList' }" class="text-white hover:bg-[#991E29] hover:border-white block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium" @click="open = false">
            最新要闻
          </RouterLink>
        </div>
      </DisclosurePanel>
    </Disclosure>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-0 sm:px-1 lg:px-2 py-6 max-w-7xl" style="max-width: 95%; padding-left: 5px; padding-right: 5px;">

      <!-- 路由视图，用于显示子路由组件 -->
      <RouterView />
    </div>
  </main>
</template>

<script setup>
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline';
</script>

<style scoped>
/* 可以添加额外的样式 */
</style>

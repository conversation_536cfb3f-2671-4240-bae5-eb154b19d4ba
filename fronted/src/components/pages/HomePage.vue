<template>
    <!-- 检查是否有API错误 -->
    <FailedToFetch v-if="hasApiError" />

    <!-- 初始加载状态：简单的加载提示 -->
    <div v-else-if="isInitializing" class="min-h-screen flex items-center justify-center">
        <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#991E29] mx-auto mb-2"></div>
            <p class="text-gray-600 text-sm">加载中...</p>
        </div>
    </div>

    <!-- 正常显示内容 -->
    <div v-else>
        <TopCarousel @api-error="handleApiError" />
        <SearchBar @api-error="handleApiError" />
        <MiddleCarousel @api-error="handleApiError" />
        <HeroSection @api-error="handleApiError" />
        <CampusHighlights @api-error="handleApiError" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'

// 引入需要的组件
import SearchBar from './index/SearchBar.vue';
import TopCarousel from './index/TopCarousel.vue';
import HeroSection from './index/HeroSection.vue';
import MiddleCarousel from './index/MiddleCarousel.vue';
import CampusHighlights from './index/CampusHighlights.vue';
import FailedToFetch from '../errors/FailedToFetch.vue';

// 状态管理
const hasApiError = ref(false)
const isInitializing = ref(true)

// 处理API错误
const handleApiError = () => {
  console.log('API错误被触发，立即显示错误页面')
  hasApiError.value = true
  isInitializing.value = false
}

// 快速健康检查
const quickHealthCheck = async () => {
  isInitializing.value = false
}

// 页面挂载时执行快速检查
onMounted(() => {
  quickHealthCheck()
})
</script>

<style>
/* 可以添加任何需要的样式 */
</style>

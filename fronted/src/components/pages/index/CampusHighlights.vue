<template>
  <!-- 校园看点轮播图部分 -->
  <div class="bg-gray-50 py-16">
    <!-- 标题部分使用常规宽度 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-10">
      <div class="flex items-center justify-between mb-10">
        <div>
          <div class="flex items-center">
            <div class="w-1 h-12 bg-[#F05454] mr-4"></div>
            <div>
              <h2 class="text-3xl font-bold text-[#333333] leading-tight">校园看点</h2>
              <p class="text-gray-500 mt-1">分享校园精彩瞬间，记录美好校园生活</p>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <button type="button" @click.stop="prevCampusSlide" class="w-10 h-10 rounded-full flex items-center justify-center bg-white shadow-md hover:bg-[#F05454] hover:text-white text-[#333333] transition-all transform hover:-translate-y-1 cursor-pointer z-20">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button type="button" @click.stop="nextCampusSlide" class="w-10 h-10 rounded-full flex items-center justify-center bg-white shadow-md hover:bg-[#F05454] hover:text-white text-[#333333] transition-all transform hover:-translate-y-1 cursor-pointer z-20">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 轮播图部分使用全宽布局 -->
    <div class="relative overflow-hidden">
      <div class="flex transition-transform duration-500 ease-in-out" :style="{ transform: `translateX(-${campusActiveSlide * 100}%)` }">
        <div v-for="(item, index) in campusNews" :key="item.id || index"
             :class="`flex-shrink-0 ${campusVisibleSlides === 1 ? 'w-full' : campusVisibleSlides === 2 ? 'w-1/2' : campusVisibleSlides === 3 ? 'w-1/3' : campusVisibleSlides === 4 ? 'w-1/4' : 'w-1/5'}`">
          <div
            class="mx-2 bg-white rounded-md overflow-hidden shadow-md hover:shadow-lg group h-full cursor-pointer transform hover:-translate-y-1 article-card"
            @click="handleArticleClick(item, $event)"
            :title="`点击查看文章：${item.title}`"
          >
            <div class="relative overflow-hidden aspect-square">
              <img
                :src="item.image"
                :alt="item.title"
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                @error="handleImageError"
                loading="lazy"
              >
              <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <!-- 添加点击提示图标 -->
              <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="bg-white/90 rounded-full p-1.5 shadow-md">
                  <svg class="w-4 h-4 text-[#F05454]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-4">
              <h3 class="text-base font-medium text-[#333333] group-hover:text-[#F05454] transition-colors line-clamp-2">{{ item.title }}</h3>
              <div class="flex items-center mt-2 text-xs text-gray-500">
                <span>{{ item.date }}</span>
                <span class="mx-2">|</span>
                <span>{{ item.category }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 指示器 -->
    <div class="flex justify-center mt-10 space-x-3 mb-4">
      <button
          v-for="(_, index) in totalPages"
          :key="index"
          @click="campusActiveSlide = index"
          :class="`h-3 rounded-full transition-all duration-300 ${campusActiveSlide === index ? 'bg-[#F05454] w-8' : 'bg-gray-300 w-3 hover:bg-gray-400'}`"
      ></button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';

// API基础URL
const API_BASE_URL = 'http://localhost:8080';

// Vue Router实例
const router = useRouter();

// 定义emit
const emit = defineEmits(['api-error'])

// 校园看点轮播图相关数据和方法
const campusActiveSlide = ref(0);
const campusVisibleSlides = ref(5); // 同时显示的卡片数量
const campusNews = ref([]);
const error = ref(null);

// 计算总页数
const totalPages = computed(() => {
  if (campusNews.value.length === 0) return 0;
  return Math.ceil(campusNews.value.length / campusVisibleSlides.value);
});

// 从HTML内容中提取第一张图片URL的函数
const extractFirstImageFromHtml = (htmlContent) => {
  if (!htmlContent) return null;

  try {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 查找所有img标签
    const images = tempDiv.querySelectorAll('img');

    // 遍历图片，找到第一个有效的图片URL
    for (let i = 0; i < images.length; i++) {
      const img = images[i];
      let src = img.getAttribute('src') || img.getAttribute('data-src');

      if (src && src.trim() !== '') {
        src = src.trim();

        // 跳过base64图片和占位符图片
        if (src.startsWith('data:') ||
            src.includes('placeholder') ||
            src.includes('loading') ||
            src.includes('blank.gif') ||
            src.includes('spacer.gif')) {
          continue;
        }

        // 处理不同类型的URL
        if (src.startsWith('http://') || src.startsWith('https://')) {
          // 绝对URL，直接返回
          return src;
        } else if (src.startsWith('//')) {
          // 协议相对URL，添加http协议
          return `http:${src}`;
        } else if (src.startsWith('/')) {
          // 根相对路径，添加基础URL
          return `${API_BASE_URL}${src}`;
        } else {
          // 相对路径，添加基础URL和斜杠
          return `${API_BASE_URL}/${src}`;
        }
      }
    }

    // 如果没有找到img标签，尝试使用正则表达式匹配
    const imgRegex = /<img[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi;
    let match;
    while ((match = imgRegex.exec(htmlContent)) !== null) {
      let src = match[1];
      if (src && src.trim() !== '') {
        src = src.trim();

        // 跳过base64图片和占位符图片
        if (src.startsWith('data:') ||
            src.includes('placeholder') ||
            src.includes('loading') ||
            src.includes('blank.gif') ||
            src.includes('spacer.gif')) {
          continue;
        }

        // 处理URL
        if (src.startsWith('http://') || src.startsWith('https://')) {
          return src;
        } else if (src.startsWith('//')) {
          return `http:${src}`;
        } else if (src.startsWith('/')) {
          return `${API_BASE_URL}${src}`;
        } else {
          return `${API_BASE_URL}/${src}`;
        }
      }
    }

  } catch (e) {
    console.warn('解析HTML内容时出错:', e);
  }

  return null; // 没有找到有效图片
};

// 格式化日期的函数
const formatDate = (dateString) => {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (e) {
    return dateString;
  }
};

// 图片加载错误处理函数
const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src);
  // 设置默认图片或隐藏图片
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDMwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xNTAgMTAwQzE2MS4wNDYgMTAwIDE3MCA5MC45NTQzIDE3MCA4MEM1NyA2OS4wNDU3IDE0OCA2MCAxNTAgNjBDMTUyIDYwIDE0MyA2OS4wNDU3IDE0MyA4MEM0MyA5MC45NTQzIDE1MiAxMDAgMTUwIDEwMFoiIGZpbGw9IiNEMUQ1REIiLz4KPHBhdGggZD0iTTEyNSAxNDBIMTc1VjE4MEgxMjVWMTQwWiIgZmlsbD0iI0QxRDVEQiIvPgo8L3N2Zz4K';
  event.target.alt = '图片加载失败';
};

// 文章卡片点击跳转函数
const handleArticleClick = (article, event) => {
  try {
    console.log('点击文章:', article.title, '文章ID:', article.id);

    // 验证文章ID是否有效
    if (!article.id) {
      console.error('文章ID无效:', article);
      return;
    }

    // 构建跳转URL
    const targetUrl = `/news/${article.id}`;
    console.log('准备跳转到:', targetUrl);

    // 检查是否按住Ctrl/Cmd键，如果是则在新标签页打开，否则在当前标签页打开
    const openInNewTab = event?.ctrlKey || event?.metaKey || true; // 默认在新标签页打开

    if (openInNewTab) {
      // 在新标签页打开
      try {
        const routeData = router.resolve({ path: targetUrl });
        window.open(routeData.href, '_blank');
      } catch (routerError) {
        console.warn('Vue Router解析失败，使用原生方式:', routerError);
        // 获取当前页面的基础URL
        const baseUrl = window.location.origin;
        const fullUrl = `${baseUrl}${targetUrl}`;
        window.open(fullUrl, '_blank');
      }
    } else {
      // 在当前标签页打开
      router.push(targetUrl);
    }

  } catch (error) {
    console.error('跳转文章详情页失败:', error);
    // 最后的备用方案：使用硬编码的URL
    const fallbackUrl = `http://localhost:5173/news/${article.id}`;
    console.log('使用备用URL:', fallbackUrl);
    window.open(fallbackUrl, '_blank');
  }
};

// 获取校园看点数据（从文章API获取）
const fetchCampusHighlights = async () => {
  error.value = null;
  try {
    // 从文章API获取最新文章，获取更多数量以便过滤
    const response = await axios.get(`${API_BASE_URL}/api/articles/latest`, {
      timeout: 5000,
      params: {
        pageNum: 1,
        pageSize: 30 // 获取30篇文章，然后过滤出有图片的
      }
    });

    let articlesWithImages = [];

    // 处理返回的数据
    const articles = response.data.list || response.data || [];
    console.log('获取到文章列表:', articles.length, '篇');

    // 批量获取文章详情，限制并发数量以避免服务器压力
    const maxConcurrent = 5; // 最多同时请求5篇文章的详情
    const targetCount = 15; // 目标获取15篇有图片的文章

    for (let i = 0; i < articles.length && articlesWithImages.length < targetCount; i += maxConcurrent) {
      // 创建当前批次的请求
      const batch = articles.slice(i, i + maxConcurrent);
      const promises = batch.map(async (article) => {
        try {
          const detailResponse = await axios.get(`${API_BASE_URL}/api/articles/${article.newsId}`, {
            timeout: 10000 // 增加到10秒
          });
          const articleDetail = detailResponse.data;

          // 从HTML内容中提取第一张图片
          const imageUrl = extractFirstImageFromHtml(articleDetail.content_html);

          if (imageUrl) {
            return {
              id: article.newsId,
              title: article.title,
              image: imageUrl,
              date: formatDate(article.pubTime),
              category: article.category || article.section || '校园动态',
              link: `/news/${article.newsId}` // 链接到文章详情页
            };
          }
          return null;
        } catch (detailErr) {
          // 只在非超时错误时显示警告，超时错误只记录到控制台
          if (detailErr.message.includes('timeout')) {
            console.log(`文章 ${article.newsId} 请求超时，跳过该文章`);
          } else {
            console.warn(`获取文章 ${article.newsId} 详情失败:`, detailErr.message);
          }
          return null;
        }
      });

      // 等待当前批次完成
      const results = await Promise.all(promises);

      // 添加有效结果
      for (const result of results) {
        if (result && articlesWithImages.length < targetCount) {
          articlesWithImages.push(result);
        }
      }

      console.log(`处理批次 ${Math.floor(i/maxConcurrent) + 1}, 当前有图片文章数量: ${articlesWithImages.length}`);
    }

    campusNews.value = articlesWithImages;
    console.log('最终获取到有图片的文章:', articlesWithImages.length, '篇');

    // 如果没有找到有图片的文章，显示错误信息
    if (campusNews.value.length === 0) {
      error.value = '暂无包含图片的校园看点数据';
    }

  } catch (err) {
    console.error('Error fetching campus highlights:', err);
    // 触发API错误事件，让父组件处理
    emit('api-error', err);
  }
};

// 切换到上一页幻灯片
const prevCampusSlide = () => {
  console.log('prevCampusSlide clicked');

  if (campusActiveSlide.value > 0) {
    // 向前移动一页
    campusActiveSlide.value--;
  } else {
    // 如果已经在第一页，则跳到最后一页
    campusActiveSlide.value = totalPages.value - 1;
  }
  console.log('After prev click, active slide:', campusActiveSlide.value);
};

// 切换到下一页幻灯片
const nextCampusSlide = () => {
  console.log('nextCampusSlide clicked');

  if (campusActiveSlide.value < totalPages.value - 1) {
    // 向后移动一页
    campusActiveSlide.value++;
  } else {
    // 如果已经是最后一页，则返回第一页
    campusActiveSlide.value = 0;
  }
  console.log('After next click, active slide:', campusActiveSlide.value);
};

// 监听窗口大小变化，调整可见幻灯片数量
const updateVisibleSlides = () => {
  const oldVisibleSlides = campusVisibleSlides.value;

  if (window.innerWidth < 640) {
    campusVisibleSlides.value = 1;
  } else if (window.innerWidth < 768) {
    campusVisibleSlides.value = 2;
  } else if (window.innerWidth < 1024) {
    campusVisibleSlides.value = 3;
  } else if (window.innerWidth < 1280) {
    campusVisibleSlides.value = 4;
  } else {
    campusVisibleSlides.value = 5;
  }

  // 如果可见幻灯片数量发生变化，重置当前页面到第一页
  if (oldVisibleSlides !== campusVisibleSlides.value) {
    campusActiveSlide.value = 0;
  }
};


// 页面加载时获取数据并调整可见幻灯片数量
onMounted(async () => {
  try {
    // 不预设默认数据，直接从API获取
    campusNews.value = [];

    // 尝试从 API 获取数据
    await fetchCampusHighlights();

    // 调整可见幻灯片数量
    updateVisibleSlides();
    window.addEventListener('resize', updateVisibleSlides);

    // 等待一个帧，确保 DOM 已经更新
    await nextTick();
  } catch (error) {
    console.error('Error in onMounted:', error);
  }
});

// 页面卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', updateVisibleSlides);
});
</script>

<style scoped>

/* 文章卡片基础样式 */
.article-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

/* 文章卡片悬停效果增强 */
.article-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 确保文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 优化的点击反馈效果 - 更加平缓 */
.article-card:active {
  transform: translateY(0px) scale(0.98);
  transition: all 0.1s cubic-bezier(0.4, 0, 0.6, 1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 点击后的优雅回弹效果 */
.article-card:not(:active) {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
}

/* 为触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .article-card:active {
    transform: scale(0.985);
    transition: transform 0.1s ease-out;
  }

  .article-card:not(:active) {
    transition: transform 0.2s ease-out;
  }
}

/* 添加微妙的点击波纹效果 */
.article-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(240, 84, 84, 0.1) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 1;
}

.article-card:active::before {
  opacity: 1;
  transform: scale(1);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 图片加载动画 */
img {
  transition: opacity 0.3s ease-in-out;
}

img[src=""] {
  opacity: 0;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .article-card:hover {
    transform: translateY(-4px);
  }

  .article-card:active {
    transform: scale(0.99);
  }
}
</style>

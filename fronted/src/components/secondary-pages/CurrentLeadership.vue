<template>
  <SecondaryPageTemplate
    page-title="现任领导"
    page-subtitle="广州南洋理工职业学院"
    breadcrumb-category="学校概况"
    breadcrumb-page="现任领导"
    sidebar-title="学校概况"
    :sidebar-items="sidebarItems"
    :content-title="(schoolInfo.school_name || '学校') + '现任领导'"
    :content-subtitle="'学校领导班子与管理团队'"
    :banner-image="bannerImage"
  >
    <template #content>
      <!-- 领导介绍 -->
      <div class="mb-8" v-if="!loading">
        <p class="text-gray-700 leading-relaxed mb-4">
          学校现任领导班子团结务实，开拓进取，致力于推动学校高质量发展，为培养高素质技术技能人才提供坚强的组织保障。
        </p>
      </div>

      <!-- 加载状态 -->
      <div class="mb-8 text-center" v-if="loading">
        <div class="inline-flex items-center px-4 py-2 text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载领导信息...
        </div>
      </div>

      <!-- 领导班子展示 -->
      <div class="space-y-8" v-if="!loading">
        <!-- 校长 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">校长</h3>
          <div class="bg-gradient-to-br from-red-50 to-orange-50 p-6 rounded-lg">
            <div class="flex items-start space-x-4">
              <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="text-lg font-semibold text-gray-900 mb-2">刘志扬</h4>
                <p class="text-gray-700 text-sm leading-relaxed">
                  主持学校全面工作。
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 执行校长 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">执行校长</h3>
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
            <div class="flex items-start space-x-4">
              <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="text-lg font-semibold text-gray-900 mb-2">姚 侃</h4>
                <p class="text-gray-700 text-sm leading-relaxed mb-2">
                  协助校长主持学校全面工作，负责人事、财务、审计等工作。
                </p>
                <p class="text-gray-600 text-xs">
                  分管：人力资源部、财务部<br>
                  联系：经济管理学院、人文与教育学院
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 党委书记 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">党委书记、省政府督导专员</h3>
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg">
            <div class="flex items-start space-x-4">
              <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="text-lg font-semibold text-gray-900 mb-2">胡庭胜</h4>
                <p class="text-gray-700 text-sm leading-relaxed mb-2">
                  主持校党委全面工作，负责组织、宣传、统战及思想政治理论课方面工作。
                </p>
                <p class="text-gray-600 text-xs">
                  分管：党委办公室、党委组织部、党委宣传部、党委统战部<br>
                  联系：马克思主义学院
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 常务副校长 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">常务副校长</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 马燕霞 -->
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-lg">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-gray-900 mb-2">马燕霞</h4>
                  <p class="text-gray-700 text-sm leading-relaxed mb-2">
                    负责综合行政、后勤、基建、教代会、工会方面工作。
                  </p>
                  <p class="text-gray-600 text-xs">
                    分管：行政党群部<br>
                    联系：卫生健康学院
                  </p>
                </div>
              </div>
            </div>

            <!-- 李柏青 -->
            <div class="bg-gradient-to-br from-yellow-50 to-orange-50 p-6 rounded-lg">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-gray-900 mb-2">李柏青</h4>
                  <p class="text-gray-700 text-sm leading-relaxed mb-2">
                    负责教学、科研、产教融合、校企合作、评建、图书、信息化、实验室建设、创新创业方面工作。
                  </p>
                  <p class="text-gray-600 text-xs">
                    分管：教学科研部、图书馆
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 副校长 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">副校长</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 岳国善 -->
            <div class="bg-gradient-to-br from-teal-50 to-cyan-50 p-4 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="font-semibold text-gray-900 mb-1">岳国善</h4>
                  <p class="text-gray-700 text-xs leading-relaxed mb-1">
                    负责招生、校友会、继续教育、培训、国际教育事务方面工作。
                  </p>
                  <p class="text-gray-600 text-xs">
                    分管：招生办公室、合作交流部<br>
                    联系：信息工程学院
                  </p>
                </div>
              </div>
            </div>

            <!-- 刘奇志 -->
            <div class="bg-gradient-to-br from-indigo-50 to-blue-50 p-4 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="font-semibold text-gray-900 mb-1">刘奇志</h4>
                  <p class="text-gray-700 text-xs leading-relaxed mb-1">
                    负责学生思想政治教育、就业、共青团、武装、心理健康教育、安全保卫方面工作。
                  </p>
                  <p class="text-gray-600 text-xs">
                    分管：学生发展部、校团委<br>
                    联系：建筑与艺术学院
                  </p>
                </div>
              </div>
            </div>

            <!-- 陈友鹏 -->
            <div class="bg-gradient-to-br from-rose-50 to-pink-50 p-4 rounded-lg">
              <div class="flex items-start space-x-3">
                <div class="w-10 h-10 bg-rose-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 616 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <div class="flex-1">
                  <h4 class="font-semibold text-gray-900 mb-1">陈友鹏</h4>
                  <p class="text-gray-700 text-xs leading-relaxed mb-1">
                    负责教学、科研、产教融合、校企合作、评建、图书、信息化、实验室建设、创新创业方面工作。
                  </p>
                  <p class="text-gray-600 text-xs">
                    分管：教学科研部、图书馆<br>
                    联系：智能工程学院
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 更新时间 -->
        <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-6 rounded-lg">
          <div class="text-center">
            <h3 class="text-lg font-semibold mb-2">领导班子信息</h3>
            <p class="text-sm opacity-90">更新时间：2025年1月4日</p>
            <p class="text-xs opacity-75 mt-2">以上信息如有变动，请以最新公告为准</p>
          </div>
        </div>
      </div>
    </template>
  </SecondaryPageTemplate>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SecondaryPageTemplate from './SecondaryPageTemplate.vue'

// 响应式数据
const loading = ref(true)
const schoolInfo = ref({})
const bannerImage = ref('')

// 侧边栏导航项目
const sidebarItems = [
  { name: '学校简介', route: { name: 'SchoolIntroduction' } },
  { name: '现任领导', route: { name: 'CurrentLeadership' }, active: true },
  { name: '历任领导', route: { name: 'FormerLeadership' } },
  { name: '学校大事记', route: { name: 'SchoolChronicle' } },
  { name: '学校荣誉', route: { name: 'SchoolHonors' } },
  { name: '组织架构', route: { name: 'OrganizationStructure' } },
  { name: '标志成果', route: { name: 'SignificantAchievements' } }
]

// 组件挂载时设置数据
onMounted(() => {
  // 设置学校信息（硬编码）
  schoolInfo.value = {
    school_name: '广州南洋理工职业学院'
  }
  
  // 设置本地横幅图片
  bannerImage.value = '/images/banners/school-honors.jpg'
  
  // 设置加载完成
  loading.value = false
})
</script>

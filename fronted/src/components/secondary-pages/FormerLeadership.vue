<template>
  <SecondaryPageTemplate
    page-title="历任领导"
    page-subtitle="广州南洋理工职业学院"
    breadcrumb-category="学校概况"
    breadcrumb-page="历任领导"
    sidebar-title="学校概况"
    :sidebar-items="sidebarItems"
    :content-title="(schoolInfo.school_name || '学校') + '历任领导'"
    :content-subtitle="'学校发展历程中的领导班子'"
    :banner-image="bannerImage"
  >
    <template #content>
      <!-- 历任领导介绍 -->
      <div class="mb-8" v-if="!loading">
        <p class="text-gray-700 leading-relaxed mb-4">
          自建校以来，学校历任领导班子秉承"育人立校、质量兴校、人才强校、特色荣校、制度治校"的办学理念，为学校的建设发展做出了重要贡献。
        </p>
      </div>

      <!-- 加载状态 -->
      <div class="mb-8 text-center" v-if="loading">
        <div class="inline-flex items-center px-4 py-2 text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载历任领导信息...
        </div>
      </div>

      <!-- 历任领导展示 -->
      <div class="space-y-8" v-if="!loading">
        <!-- 历任领导图片展示 -->
        <div class="bg-gradient-to-br from-[#C32A31]/5 to-[#991E29]/5 rounded-lg p-8">
          <div class="text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-6">历任领导班子</h3>

            <!-- 历任领导图片占位符 -->
            <div class="max-w-4xl mx-auto mb-6">
              <div class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg shadow-lg overflow-hidden">
                <div class="w-full h-64 flex items-center justify-center text-gray-500">
                  <div class="text-center">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"/>
                    </svg>
                    <p class="text-sm font-medium">历任领导班子合影</p>
                    <p class="text-xs text-gray-400 mt-1">图片待添加</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 图片说明 -->
            <p class="text-gray-600 text-sm italic">
              历任领导班子合影（数据更新：2023年9月25日）
            </p>
          </div>
        </div>

        <!-- 历任领导时期划分 -->
        <div class="space-y-6">
          <!-- 建校初期 -->
          <div class="border-l-4 border-[#C32A31] pl-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">建校初期（2001-2010）</h3>
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                  <h4 class="font-semibold text-gray-800">创校领导团队</h4>
                  <p class="text-gray-700 text-sm leading-relaxed">
                    在建校初期，学校领导班子致力于建立完善的办学体系，确立了学校的办学方向和发展目标，为学校后续发展奠定了坚实基础。
                  </p>
                </div>
                <div class="space-y-4">
                  <h4 class="font-semibold text-gray-800">主要成就</h4>
                  <ul class="text-gray-700 text-sm space-y-1">
                    <li>• 确立办学理念和发展方向</li>
                    <li>• 建立基本的教学管理体系</li>
                    <li>• 初步形成专业设置框架</li>
                    <li>• 建设基础教学设施</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- 发展期 -->
          <div class="border-l-4 border-[#991E29] pl-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">快速发展期（2010-2020）</h3>
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                  <h4 class="font-semibold text-gray-800">发展期领导团队</h4>
                  <p class="text-gray-700 text-sm leading-relaxed">
                    这一时期的领导班子推动学校实现跨越式发展，新校园建设完成，办学规模不断扩大，教学质量显著提升。
                  </p>
                </div>
                <div class="space-y-4">
                  <h4 class="font-semibold text-gray-800">重要里程碑</h4>
                  <ul class="text-gray-700 text-sm space-y-1">
                    <li>• 新校园建设并投入使用</li>
                    <li>• 通过人才培养工作评估</li>
                    <li>• 学生规模突破万人</li>
                    <li>• 获得多项省级荣誉</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- 提升期 -->
          <div class="border-l-4 border-[#C32A31] pl-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">质量提升期（2020至今）</h3>
            <div class="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                  <h4 class="font-semibold text-gray-800">现代化建设</h4>
                  <p class="text-gray-700 text-sm leading-relaxed">
                    近年来的领导班子注重内涵建设，推进教育教学改革，提升人才培养质量，努力建设特色鲜明的高水平职业院校。
                  </p>
                </div>
                <div class="space-y-4">
                  <h4 class="font-semibold text-gray-800">发展重点</h4>
                  <ul class="text-gray-700 text-sm space-y-1">
                    <li>• 深化产教融合校企合作</li>
                    <li>• 推进教育教学改革</li>
                    <li>• 加强师资队伍建设</li>
                    <li>• 提升社会服务能力</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 传承与发展 -->
        <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-8 rounded-lg">
          <div class="text-center">
            <h3 class="text-xl font-semibold mb-4">传承与发展</h3>
            <p class="text-sm leading-relaxed opacity-90 max-w-3xl mx-auto">
              历任领导班子秉承"育人立校、质量兴校、人才强校、特色荣校、制度治校"的办学理念，
              在不同的历史时期都为学校发展做出了重要贡献。他们的智慧和努力，
              为学校今天的成就奠定了坚实基础，也为未来的发展指明了方向。
            </p>
          </div>
        </div>

        <!-- 数据来源说明 -->
        <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-gray-400">
          <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-gray-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
            </svg>
            <div>
              <h4 class="font-semibold text-gray-800 mb-1">数据来源</h4>
              <p class="text-gray-600 text-sm leading-relaxed">
                本页面信息数据更新时间：2023年9月25日。
                如需了解更详细的历任领导信息，请联系学校档案室或党委办公室。
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </SecondaryPageTemplate>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SecondaryPageTemplate from './SecondaryPageTemplate.vue'

// 响应式数据
const loading = ref(true)
const schoolInfo = ref({})
const bannerImage = ref('')

// 侧边栏导航项目
const sidebarItems = [
  { name: '学校简介', route: { name: 'SchoolIntroduction' } },
  { name: '现任领导', route: { name: 'CurrentLeadership' } },
  { name: '历任领导', route: { name: 'FormerLeadership' }, active: true },
  { name: '学校大事记', route: { name: 'SchoolChronicle' } },
  { name: '学校荣誉', route: { name: 'SchoolHonors' } },
  { name: '组织架构', route: { name: 'OrganizationStructure' } },
  { name: '标志成果', route: { name: 'SignificantAchievements' } }
]



// 组件挂载时设置数据
onMounted(() => {
  // 设置学校信息（硬编码）
  schoolInfo.value = {
    school_name: '广州南洋理工职业学院'
  }

  // 设置本地横幅图片
  bannerImage.value = '/images/banners/significant-achievements.jpg'

  // 设置加载完成
  loading.value = false
})
</script>

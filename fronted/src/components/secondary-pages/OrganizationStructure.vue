<template>
  <SecondaryPageTemplate
    page-title="组织架构"
    page-subtitle="广州南洋理工职业学院"
    breadcrumb-category="学校概况"
    breadcrumb-page="组织架构"
    sidebar-title="学校概况"
    :sidebar-items="sidebarItems"
    :content-title="(schoolInfo.school_name || '学校') + '组织架构'"
    :content-subtitle="'学校管理体系与组织结构'"
    :banner-image="bannerImage"
  >
    <template #content>
      <!-- 组织架构介绍 -->
      <div class="mb-8" v-if="!loading">
        <p class="text-gray-700 leading-relaxed mb-4">
          学校建立了完善的组织架构和管理体系，推行大部门制，淡化行政色彩，形成了科学高效的治理结构。
        </p>
      </div>

      <!-- 加载状态 -->
      <div class="mb-8 text-center" v-if="loading">
        <div class="inline-flex items-center px-4 py-2 text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载组织架构信息...
        </div>
      </div>

      <!-- 组织架构内容说明 -->
      <div class="mb-8" v-if="!loading">
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-lg">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                <strong>温馨提示：</strong>我校正在完善相关信息的展示，如有不便敬请谅解。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 管理理念展示 -->
      <div class="space-y-8" v-if="!loading">
        <!-- 管理理念 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">管理理念</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
              <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <h4 class="text-lg font-semibold text-gray-900">大部门制</h4>
              </div>
              <p class="text-gray-700 text-sm">推行大部门制管理模式，优化组织结构，提高管理效率</p>
            </div>

            <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg">
              <div class="flex items-center mb-3">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <h4 class="text-lg font-semibold text-gray-900">淡化行政色彩</h4>
              </div>
              <p class="text-gray-700 text-sm">减少行政层级，强化服务功能，提升管理服务水平</p>
            </div>
          </div>
        </div>

        <!-- 主要机构设置 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">主要机构设置</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- 教学机构 -->
            <div class="bg-white p-4 rounded-lg shadow-md border-t-4 border-blue-500">
              <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"/>
                </svg>
                教学机构
              </h4>
              <ul class="text-sm text-gray-600 space-y-1">
                <li>• 建筑与艺术学院</li>
                <li>• 数字商学院（原经济管理学院）</li>
                <li>• 智能工程学院</li>
                <li>• 人工智能学院（原信息工程学院）</li>
                <li>• 人文与教育学院</li>
                <li>• 马克思主义学院</li>
                <li>• 卫生健康学院</li>
              </ul>
            </div>

            <!-- 党群机构 -->
            <div class="bg-white p-4 rounded-lg shadow-md border-t-4 border-green-500">
              <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm6 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"/>
                </svg>
                党群机构
              </h4>
              <ul class="text-sm text-gray-600 space-y-1">
                <li>• 党群工作办公室</li>
                <li>• 组织部</li>
                <li>• 统战部</li>
                <li>• 宣传部</li>
                <li>• 工会</li>
                <li>• 关工委</li>
                <li>• 团委</li>
              </ul>
            </div>

            <!-- 行政教辅机构 -->
            <div class="bg-white p-4 rounded-lg shadow-md border-t-4 border-purple-500">
              <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <svg class="w-5 h-5 text-purple-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clip-rule="evenodd"/>
                </svg>
                行政教辅机构
              </h4>
              <ul class="text-sm text-gray-600 space-y-1">
                <li>• 行政党群部</li>
                <li>• 教学科研部</li>
                <li>• 人力资源部</li>
                <li>• 学生发展部</li>
                <li>• 合作交流部</li>
                <li>• 招生办公室</li>
                <li>• 图书馆</li>
                <li>• 财务部</li>
                <li>• 教育技术与网络中心</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 治理特色 -->
        <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-6 rounded-lg">
          <h3 class="text-xl font-semibold mb-4">治理特色</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">扁平化</div>
              <div class="text-sm opacity-90">管理层级</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">专业化</div>
              <div class="text-sm opacity-90">服务团队</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">高效化</div>
              <div class="text-sm opacity-90">运行机制</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </SecondaryPageTemplate>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SecondaryPageTemplate from './SecondaryPageTemplate.vue'

// 响应式数据
const loading = ref(true)
const schoolInfo = ref({})
const bannerImage = ref('')

// 侧边栏导航项目
const sidebarItems = [
  { name: '学校简介', route: { name: 'SchoolIntroduction' } },
  { name: '现任领导', route: { name: 'CurrentLeadership' } },
  { name: '历任领导', route: { name: 'FormerLeadership' } },
  { name: '学校大事记', route: { name: 'SchoolChronicle' } },
  { name: '学校荣誉', route: { name: 'SchoolHonors' } },
  { name: '组织架构', route: { name: 'OrganizationStructure' }, active: true },
  { name: '标志成果', route: { name: 'SignificantAchievements' } }
]

// 组件挂载时设置数据
onMounted(() => {
  // 设置学校信息（硬编码）
  schoolInfo.value = {
    school_name: '广州南洋理工职业学院'
  }

  // 设置本地横幅图片
  bannerImage.value = '/images/banners/significant-achievements.jpg'

  // 设置加载完成
  loading.value = false
})
</script>

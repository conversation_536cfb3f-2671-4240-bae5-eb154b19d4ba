<template>
  <SecondaryPageTemplate
    page-title="学校大事记"
    page-subtitle="广州南洋理工职业学院"
    breadcrumb-category="学校概况"
    breadcrumb-page="学校大事记"
    sidebar-title="学校概况"
    :sidebar-items="sidebarItems"
    :content-title="(schoolInfo.school_name || '学校') + '大事记'"
    :content-subtitle="'记录学校发展历程中的重要时刻'"
    :banner-image="bannerImage"
  >
    <template #content>
      <!-- 大事记介绍 -->
      <div class="mb-8" v-if="!loading">
        <p class="text-gray-700 leading-relaxed mb-4">
          学校大事记记录了广州南洋理工职业学院发展历程中的重要事件和里程碑，见证了学校从创建到发展壮大的光辉历程。
        </p>
      </div>

      <!-- 加载状态 -->
      <div class="mb-8 text-center" v-if="loading">
        <div class="inline-flex items-center px-4 py-2 text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载学校大事记...
        </div>
      </div>

      <!-- 大事记时间线 -->
      <div class="space-y-8" v-if="!loading">
        <!-- 2013年度 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <div class="bg-gradient-to-br from-red-50 to-orange-50 rounded-lg p-6">
            <h3 class="text-xl font-bold text-[#C32A31] mb-4 flex items-center">
              <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
              </svg>
              2013年度
            </h3>
            <div class="space-y-3 text-sm">
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">1月9日</span>
                <p class="text-gray-700">省教育厅评估回访专家组来我院进行工作检查，专家组肯定了我院整改工作的成绩。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">3月28日</span>
                <p class="text-gray-700">第十四届校园科技文化艺术节隆重开幕。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">6月28日</span>
                <p class="text-gray-700">我院学生参加2013年全国职业院校技能大赛，"汽车营销"项目荣获高职组三等奖，实现省教育厅技能大赛奖零的突破。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">11月10日</span>
                <p class="text-gray-700">学院举行2014届毕业生校园招聘会，共吸纳了216家企业参与，共有1036名学子初步与用人单位达成意向。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">12月28日</span>
                <p class="text-gray-700">学院召开校友总会成立大会，举办2013级新生家长联谊会。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 2012年度 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg p-6">
            <h3 class="text-xl font-bold text-[#991E29] mb-4 flex items-center">
              <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
              </svg>
              2012年度
            </h3>
            <div class="space-y-3 text-sm">
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">3月28日</span>
                <p class="text-gray-700">学院第一届教职员工代表大会在图书馆隆重召开。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">9月26日</span>
                <p class="text-gray-700">广东省教育考试院院长杨开乔一行莅临我院视察工作，肯定了南洋近几年取得办学成果。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">11月</span>
                <p class="text-gray-700">我院荣获"2012年广东最具社会认可度学院"、"最具竞争力广东民办高校"称号。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">12月29日</span>
                <p class="text-gray-700">召开第三届新生家长联谊会。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 2011年度 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6">
            <h3 class="text-xl font-bold text-[#C32A31] mb-4 flex items-center">
              <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
              </svg>
              2011年度
            </h3>
            <div class="space-y-3 text-sm">
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">9月6日</span>
                <p class="text-gray-700">全国人大常委会副委员长、民革中央主席周铁农视察我院，充分肯定我院的办学成绩。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">3-12月</span>
                <p class="text-gray-700">一年完成二大评估：通过省教育厅人才培养工作评估和思政课教学工作评估。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#C32A31] font-semibold min-w-0 flex-shrink-0">全年</span>
                <p class="text-gray-700">以"二基一能"为核心的系列教学改革稳步推进，毕业生就业率达98.4%。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 2010年度 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <div class="bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg p-6">
            <h3 class="text-xl font-bold text-[#991E29] mb-4 flex items-center">
              <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
              </svg>
              2010年度
            </h3>
            <div class="space-y-3 text-sm">
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">3月30日</span>
                <p class="text-gray-700">陈桂荣同志出任广州南洋理工职业学院党委书记。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">4月27日</span>
                <p class="text-gray-700">广州地区高校第一支"学生军政教导队"在我院成立。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">8月底</span>
                <p class="text-gray-700">首期占地308亩、建筑面积14万多平方米的南洋新校园落成启用，学院整体迁入。</p>
              </div>
              <div class="flex items-start space-x-3">
                <span class="text-[#991E29] font-semibold min-w-0 flex-shrink-0">12月1日</span>
                <p class="text-gray-700">我院荣获"2010年度广东最具就业竞争力民办学院"称号。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 重要成就总结 -->
        <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-8 rounded-lg">
          <div class="text-center">
            <h3 class="text-xl font-semibold mb-4">发展成就</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div>
                <h4 class="font-semibold mb-2">教学质量</h4>
                <p class="opacity-90">通过多项省级评估，教学改革成效显著，人才培养质量不断提升</p>
              </div>
              <div>
                <h4 class="font-semibold mb-2">校园建设</h4>
                <p class="opacity-90">新校园建成启用，办学条件显著改善，为学生提供优质学习环境</p>
              </div>
              <div>
                <h4 class="font-semibold mb-2">社会认可</h4>
                <p class="opacity-90">获得多项省级荣誉，社会声誉不断提升，就业竞争力持续增强</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据来源说明 -->
        <div class="bg-gray-50 p-6 rounded-lg border-l-4 border-gray-400">
          <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-gray-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
            </svg>
            <div>
              <h4 class="font-semibold text-gray-800 mb-1">数据来源</h4>
              <p class="text-gray-600 text-sm leading-relaxed">
                本页面大事记内容因档案缺失，仅记录了2010-2013年期间的重要事件。
                如需了解更完整的学校发展历程，请联系学校档案室或党委办公室。
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </SecondaryPageTemplate>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SecondaryPageTemplate from './SecondaryPageTemplate.vue'

// 响应式数据
const loading = ref(true)
const schoolInfo = ref({})
const bannerImage = ref('')

// 侧边栏导航项目
const sidebarItems = [
  { name: '学校简介', route: { name: 'SchoolIntroduction' } },
  { name: '现任领导', route: { name: 'CurrentLeadership' } },
  { name: '历任领导', route: { name: 'FormerLeadership' } },
  { name: '学校大事记', route: { name: 'SchoolChronicle' }, active: true },
  { name: '学校荣誉', route: { name: 'SchoolHonors' } },
  { name: '组织架构', route: { name: 'OrganizationStructure' } },
  { name: '标志成果', route: { name: 'SignificantAchievements' } }
]

// 组件挂载时设置数据
onMounted(() => {
  // 设置学校信息（硬编码）
  schoolInfo.value = {
    school_name: '广州南洋理工职业学院'
  }
  
  // 设置本地横幅图片
  bannerImage.value = '/images/banners/school-chronicle.jpg'
  
  // 设置加载完成
  loading.value = false
})
</script>

<template>
  <SecondaryPageTemplate
    page-title="学校简介"
    page-subtitle="广州南洋理工职业学院"
    breadcrumb-category="学校概况"
    breadcrumb-page="学校简介"
    sidebar-title="学校概况"
    :sidebar-items="sidebarItems"
    :content-title="(schoolInfo.school_name || '学校') + '概况'"
    :content-subtitle="schoolInfo.school_code ? '（院校代码：' + schoolInfo.school_code + '）' : ''"
    :banner-image="bannerImage"
  >
    <template #content>
      <!-- 学校介绍内容 -->
      <div class="mb-8" v-if="!loading">
        <p class="text-gray-700 leading-relaxed mb-4">
          {{ schoolInfo.schoolIntroduction || '学校简介加载中...' }}
        </p>
        <p class="text-gray-700 leading-relaxed">
          {{ schoolInfo.schoolMission || '办学理念和目标加载中...' }}
        </p>
      </div>

      <!-- 加载状态 -->
      <div class="mb-8 text-center" v-if="loading">
        <div class="inline-flex items-center px-4 py-2 text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载学校信息...
        </div>
      </div>

      <!-- 办学特色亮点 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
          <div class="flex items-center mb-3">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">办学理念</h3>
          </div>
          <p class="text-gray-700 text-sm">{{ schoolInfo.schoolPhilosophy || '办学理念加载中...' }}</p>
        </div>

        <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg">
          <div class="flex items-center mb-3">
            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0zM18 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM14 15a4 4 0 0 0-8 0v3h8v-3z"/>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900">培养目标</h3>
          </div>
          <p class="text-gray-700 text-sm">{{ schoolInfo.trainingGoals || '培养目标加载中...' }}</p>
        </div>
      </div>

      <!-- 关键数据展示 -->
      <div class="bg-gray-50 rounded-lg p-6 mb-8" v-if="!loading">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 text-left">办学规模</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div v-for="stat in statistics.slice(0, 4)" :key="stat.id" class="text-center">
            <div class="text-2xl font-bold text-[#C32A31] mb-1 animate-number"
                 :data-target="stat.statValue + (stat.statUnit || '')"
                 :data-duration="1200"
                 :data-has-unit="!!(stat.statUnit)">
              {{ stat.statValue }}{{ stat.statUnit || '' }}
            </div>
            <div class="text-sm text-gray-600">{{ stat.statName }}</div>
          </div>
        </div>
      </div>

      <!-- 办学成就板块 -->
      <div class="space-y-8">
        <!-- 校园办学条件 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">校园办学条件全面优化</h3>
          <p class="text-gray-700 leading-relaxed mb-4">
            学校设有7个二级学院，专业设置以工科类专业为主，医、教、文、艺、经、管类专业协调发展。与澳门中西创新学院、广东科技学院、广州华南商贸职业学院同属广东南博教育集团旗下高等院校，依托南博集团雄厚的办学实力与集团化办学优势，取得了卓越的办学成绩。
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div class="bg-blue-50 p-4 rounded-lg">
              <div class="text-lg font-semibold text-blue-700 mb-1 animate-number"
                   data-target="156个"
                   data-duration="1000"
                   data-has-unit="true">156个</div>
              <div class="text-sm text-gray-600">专业实验室（场）</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <div class="text-lg font-semibold text-green-700 mb-1 animate-number"
                   data-target="1.39亿元"
                   data-duration="1200"
                   data-has-unit="true">1.39亿元</div>
              <div class="text-sm text-gray-600">教学仪器设备总值</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg">
              <div class="text-lg font-semibold text-purple-700 mb-1 animate-number"
                   data-target="152万册"
                   data-duration="1100"
                   data-has-unit="true">152万册</div>
              <div class="text-sm text-gray-600">图书资料</div>
            </div>
          </div>
        </div>

        <!-- 教育教学改革 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">教育教学改革成果丰硕</h3>
          <p class="text-gray-700 leading-relaxed mb-4">
            学校现有招生专业50个，拥有国家级骨干专业1个、省级高水平专业群4个、省级品牌专业1个。获批省级"现代学徒制"试点专业23个，教育部"1+X"证书制度试点项目36个。
          </p>
          <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="text-center">
                <div class="text-lg font-semibold text-orange-700">国家级奖项</div>
                <div class="text-2xl font-bold text-orange-600 animate-number"
                     data-target="8项"
                     data-duration="800"
                     data-has-unit="true">8项</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-orange-700">省级奖项</div>
                <div class="text-2xl font-bold text-orange-600 animate-number"
                     data-target="13项"
                     data-duration="900"
                     data-has-unit="true">13项</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-orange-700">市级奖项</div>
                <div class="text-2xl font-bold text-orange-600 animate-number"
                     data-target="5项"
                     data-duration="700"
                     data-has-unit="true">5项</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 校企协同育人 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">校企协同育人成效显著</h3>
          <p class="text-gray-700 leading-relaxed mb-4">
            学校紧紧抓住粤港澳大湾区建设重大机遇，坚持市场导向，大力探索"产教跨界发展、校企精准育人"的协同育人机制，建成了3个广东省示范性产业学院。
          </p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-indigo-50 p-4 rounded-lg">
              <div class="text-lg font-semibold text-indigo-700 mb-2">合作企业</div>
              <div class="text-2xl font-bold text-indigo-600 mb-1 animate-number"
                   data-target="539家"
                   data-duration="1300"
                   data-has-unit="true">539家</div>
              <div class="text-sm text-gray-600">包含产教融合型企业<span class="animate-number"
                   data-target="24"
                   data-duration="600"
                   data-has-unit="false">24</span>家</div>
            </div>
            <div class="bg-teal-50 p-4 rounded-lg">
              <div class="text-lg font-semibold text-teal-700 mb-2">订单特色班</div>
              <div class="text-2xl font-bold text-teal-600 mb-1 animate-number"
                   data-target="144个"
                   data-duration="1000"
                   data-has-unit="true">144个</div>
              <div class="text-sm text-gray-600">基本实现专业全覆盖</div>
            </div>
          </div>
        </div>

        <!-- 人才培养质量 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-4">人才培养质量大幅提升</h3>
          <p class="text-gray-700 leading-relaxed mb-4">
            近年来，师生在国家、省级技能赛场上斩获佳绩，学生在各类技能大赛中成绩优异，先后获国家级奖项<span class="font-semibold text-[#C32A31] animate-number"
            data-target="87"
            data-duration="800"
            data-has-unit="false">87</span>个、省级奖项<span class="font-semibold text-[#C32A31] animate-number"
            data-target="457"
            data-duration="1200"
            data-has-unit="false">457</span>个。<span class="font-semibold text-[#C32A31] animate-number"
            data-target="2000"
            data-duration="1500"
            data-has-unit="false">2000</span>多名学生通过专插本考试被华南师范大学等本科院校录取。
          </p>
        </div>

        <!-- 综合办学实力 -->
        <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-6 rounded-lg">
          <h3 class="text-xl font-semibold mb-4">综合办学实力位居同类院校前列</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p class="text-sm opacity-90 mb-2">
                学校荣获教育部首批"全国国防教育特色学校"、民政部"全国民办非企业单位优秀服务品牌"单位等多项荣誉。
              </p>
            </div>
            <div>
              <p class="text-sm opacity-90">
                在GDI高职高专排行榜（<span class="font-semibold">2024</span>）中位于全国民办高职高专院校第<span class="font-semibold text-yellow-300 animate-number"
                data-target="3"
                data-duration="600"
                data-has-unit="false">3</span>名、广东省第<span class="font-semibold text-yellow-300 animate-number"
                data-target="2"
                data-duration="500"
                data-has-unit="false">2</span>名。
              </p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </SecondaryPageTemplate>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import axios from 'axios'
import SecondaryPageTemplate from './SecondaryPageTemplate.vue'

// 响应式数据
const loading = ref(true)
const schoolInfo = ref({})
const statistics = ref([])
const achievements = ref([])
const bannerImage = ref('')

// 数字动画相关
const animatedNumbers = ref(new Map())
const observers = ref([])

// 数字动画函数
const animateNumber = (element, targetValue, duration = 1000, hasUnit = false) => {
  const startValue = 0
  const startTime = performance.now()

  // 提取数字和单位
  const targetStr = targetValue.toString()
  const numericMatch = targetStr.match(/[\d.]+/)
  const numericValue = numericMatch ? parseFloat(numericMatch[0]) : 0
  const unit = hasUnit ? targetStr.replace(/[\d.]+/g, '') : ''

  const animate = (currentTime) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用 ease-out 缓动函数
    const easeOut = 1 - Math.pow(1 - progress, 3)
    const currentValue = startValue + (numericValue - startValue) * easeOut

    // 格式化数字显示
    let displayValue
    if (targetStr.includes('万')) {
      // 如果目标值包含"万"，保持万的格式
      if (targetStr.includes('.')) {
        displayValue = (currentValue).toFixed(2)
      } else {
        displayValue = Math.floor(currentValue)
      }
    } else if (targetStr.includes('亿')) {
      // 如果目标值包含"亿"，保持亿的格式
      displayValue = (currentValue).toFixed(2)
    } else if (numericValue >= 1000 && !hasUnit) {
      // 大于1000的纯数字使用千分位分隔符
      displayValue = Math.floor(currentValue).toLocaleString()
    } else if (numericValue === 2024) {
      displayValue = Math.floor(currentValue)
    }
    else {
      // 其他情况直接显示整数
      displayValue = Math.floor(currentValue)
    }

    element.textContent = displayValue + unit

    if (progress < 1) {
      requestAnimationFrame(animate)
    } else {
      // 动画完成，显示最终值
      element.textContent = targetValue
    }
  }

  requestAnimationFrame(animate)
}

// 创建 Intersection Observer
const createObserver = (element, targetValue, duration = 1000, hasUnit = false) => {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !animatedNumbers.value.has(element)) {
          animatedNumbers.value.set(element, true)
          animateNumber(element, targetValue, duration, hasUnit)
          observer.unobserve(element)
        }
      })
    },
    {
      threshold: 0.3,
      rootMargin: '0px 0px -50px 0px'
    }
  )

  observer.observe(element)
  observers.value.push(observer)
}

// 初始化数字动画
const initNumberAnimations = async () => {
  await nextTick()

  // 为所有数字元素添加动画
  const numberElements = document.querySelectorAll('.animate-number')

  numberElements.forEach((element) => {
    const targetValue = element.getAttribute('data-target')
    const duration = parseInt(element.getAttribute('data-duration')) || 1000
    const hasUnit = element.getAttribute('data-has-unit') === 'true'

    if (targetValue) {
      // 初始显示为0
      element.textContent = hasUnit ? '0' + targetValue.replace(/[\d.]/g, '') : '0'
      createObserver(element, targetValue, duration, hasUnit)
    }
  })
}

// 侧边栏导航项目
const sidebarItems = [
  { name: '学校简介', route: { name: 'SchoolIntroduction' }, active: true },
  { name: '现任领导', route: { name: 'CurrentLeadership' } },
  { name: '历任领导', route: { name: 'FormerLeadership' } },
  { name: '学校大事记', route: { name: 'SchoolChronicle' } },
  { name: '学校荣誉', route: { name: 'SchoolHonors' } },
  { name: '组织架构', route: { name: 'OrganizationStructure' } },
  { name: '标志成果', route: { name: 'SignificantAchievements' } }
]

// 获取学校简介数据
const fetchSchoolData = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/school/introduction')

    if (response.data) {
      schoolInfo.value = response.data.schoolInfo || {}
      statistics.value = response.data.statistics || []
      achievements.value = response.data.achievements || []
    }
  } catch (error) {
    console.error('获取学校数据失败:', error)
  } finally {
    loading.value = false
    // 数据加载完成后初始化动画
    await nextTick()
    initNumberAnimations()
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSchoolData()
  // 设置本地横幅图片
  bannerImage.value = '/images/banners/school-introduction.jpg'
})

// 组件卸载时清理观察器
onUnmounted(() => {
  observers.value.forEach(observer => {
    observer.disconnect()
  })
  observers.value = []
  animatedNumbers.value.clear()
})
</script>

<style scoped>
/* 数字动画相关样式 */
.animate-number {
  display: inline-block;
  transition: transform 0.2s ease-out;
}

.animate-number:hover {
  transform: scale(1.05);
}

/* 为动画数字添加轻微的发光效果 */
.animate-number.text-2xl {
  text-shadow: 0 0 10px rgba(195, 42, 49, 0.3);
}

.animate-number.text-lg {
  text-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .animate-number {
    font-size: 0.9em;
  }
}

/* 减少动画对性能的影响 */
@media (prefers-reduced-motion: reduce) {
  .animate-number {
    transition: none;
  }

  .animate-number:hover {
    transform: none;
  }
}
</style>

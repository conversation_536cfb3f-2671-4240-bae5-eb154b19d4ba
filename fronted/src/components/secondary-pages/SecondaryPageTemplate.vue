<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部横幅 -->
    <div class="relative overflow-hidden h-96">
      <!-- 背景图片层 -->
      <div class="absolute inset-0">
        <img
          v-if="currentBannerImage"
          :src="currentBannerImage"
          :alt="pageTitle"
          class="w-full h-full object-cover"
          @error="handleImageError"
        >
      </div>

      <!-- 轻微遮罩层（仅为了文字可读性） -->
      <div v-if="currentBannerImage" class="absolute inset-0 bg-black/20"></div>

      <!-- 内容层 -->
      <div class="relative z-10 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center">
            <h1 class="text-5xl font-bold mb-6 drop-shadow-lg">{{ pageTitle }}</h1>
            <p class="text-xl opacity-95 mb-8 drop-shadow">{{ pageSubtitle }}</p>
            <div class="flex justify-center">
              <nav class="flex space-x-4 text-sm bg-black/20 backdrop-blur-sm rounded-full px-6 py-3">
                <RouterLink :to="{ name: 'HomePage' }" class="hover:text-yellow-300 transition-colors">
                  首页
                </RouterLink>
                <span class="text-gray-300">/</span>
                <span class="text-yellow-300">{{ breadcrumbCategory }}</span>
                <span class="text-gray-300">/</span>
                <span class="text-yellow-300">{{ breadcrumbPage }}</span>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <!-- 装饰性几何图形 -->
      <div class="absolute top-0 right-0 w-64 h-64 opacity-10">
        <svg viewBox="0 0 200 200" class="w-full h-full">
          <circle cx="100" cy="100" r="80" fill="none" stroke="currentColor" stroke-width="2"/>
          <circle cx="100" cy="100" r="60" fill="none" stroke="currentColor" stroke-width="1"/>
          <circle cx="100" cy="100" r="40" fill="none" stroke="currentColor" stroke-width="1"/>
        </svg>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-8xl px-4 sm:px-6 lg:px-8 py-12 ml-0 lg:ml-8 mr-0 lg:mr-16">
      <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">

        <!-- 左侧导航栏 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-xl shadow-lg overflow-hidden sticky top-6">
            <!-- 导航标题 -->
            <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-6">
              <h3 class="text-lg font-bold flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
                {{ sidebarTitle }}
              </h3>
            </div>

            <!-- 导航菜单 -->
            <nav class="p-2">
              <template v-for="item in sidebarItems" :key="item.name">
                <router-link
                  v-if="item.route"
                  :to="item.route"
                  :class="[
                    'group flex items-center px-4 py-3 my-1 text-sm font-medium rounded-lg transition-all duration-200 relative overflow-hidden',
                    item.active
                      ? 'text-white bg-gradient-to-r from-[#C32A31] to-[#991E29] shadow-md transform scale-105'
                      : 'text-gray-700 hover:text-[#C32A31] hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 hover:shadow-sm'
                  ]">
                  <!-- 激活状态的装饰线 -->
                  <div v-if="item.active" class="absolute left-0 top-0 bottom-0 w-1 bg-yellow-400 rounded-r"></div>

                  <!-- 图标 -->
                  <div class="flex-shrink-0 mr-3">
                    <div :class="[
                      'w-2 h-2 rounded-full transition-all duration-200',
                      item.active ? 'bg-yellow-400' : 'bg-gray-400 group-hover:bg-[#C32A31]'
                    ]"></div>
                  </div>

                  <!-- 文字 -->
                  <span class="flex-1">{{ item.name }}</span>

                  <!-- 右侧箭头（激活状态） -->
                  <svg v-if="item.active" class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                  </svg>
                </router-link>

                <a
                  v-else
                  :href="item.href || '#'"
                  :class="[
                    'group flex items-center px-4 py-3 my-1 text-sm font-medium rounded-lg transition-all duration-200 relative overflow-hidden',
                    item.active
                      ? 'text-white bg-gradient-to-r from-[#C32A31] to-[#991E29] shadow-md transform scale-105'
                      : 'text-gray-700 hover:text-[#C32A31] hover:bg-gradient-to-r hover:from-red-50 hover:to-orange-50 hover:shadow-sm'
                  ]">
                  <!-- 激活状态的装饰线 -->
                  <div v-if="item.active" class="absolute left-0 top-0 bottom-0 w-1 bg-yellow-400 rounded-r"></div>

                  <!-- 图标 -->
                  <div class="flex-shrink-0 mr-3">
                    <div :class="[
                      'w-2 h-2 rounded-full transition-all duration-200',
                      item.active ? 'bg-yellow-400' : 'bg-gray-400 group-hover:bg-[#C32A31]'
                    ]"></div>
                  </div>

                  <!-- 文字 -->
                  <span class="flex-1">{{ item.name }}</span>

                  <!-- 右侧箭头（激活状态） -->
                  <svg v-if="item.active" class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                  </svg>
                </a>
              </template>
            </nav>

            <!-- 底部装饰 -->
            <div class="p-4 bg-gray-50 border-t">
              <div class="text-xs text-gray-500 text-center">
                <svg class="w-4 h-4 mx-auto mb-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                校办电话：020-37987237
              </div>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="lg:col-span-4">
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-8">
              <!-- 页面标题 -->
              <div class="text-left mb-8" v-if="showContentTitle">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                  {{ contentTitle }}
                </h2>
                <p class="text-sm text-gray-500" v-if="contentSubtitle">{{ contentSubtitle }}</p>
              </div>

              <!-- 主要内容插槽 -->
              <div class="prose prose-lg max-w-none">
                <slot name="content">
                  <p class="text-gray-700">请在此处添加页面内容...</p>
                </slot>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

// 定义组件属性
const props = defineProps({
  // 页面标题
  pageTitle: {
    type: String,
    default: '页面标题'
  },
  // 页面副标题
  pageSubtitle: {
    type: String,
    default: '广州南洋理工职业学院'
  },
  // 面包屑导航 - 分类
  breadcrumbCategory: {
    type: String,
    default: '分类'
  },
  // 面包屑导航 - 页面
  breadcrumbPage: {
    type: String,
    default: '页面'
  },
  // 侧边栏标题
  sidebarTitle: {
    type: String,
    default: '导航'
  },
  // 侧边栏项目
  sidebarItems: {
    type: Array,
    default: () => []
  },
  // 内容区标题
  contentTitle: {
    type: String,
    default: ''
  },
  // 内容区副标题
  contentSubtitle: {
    type: String,
    default: ''
  },
  // 是否显示内容标题
  showContentTitle: {
    type: Boolean,
    default: true
  },
  // 横幅图片URL（可选）
  bannerImage: {
    type: String,
    default: ''
  }
})

// 响应式数据
const currentBannerImage = ref(props.bannerImage)

// 图片加载错误处理
const handleImageError = (event) => {
  console.warn('横幅图片加载失败:', event.target.src)
  // 图片加载失败时隐藏图片，显示纯色背景
  currentBannerImage.value = ''
}

// 监听 bannerImage prop 的变化
watch(() => props.bannerImage, (newValue) => {
  if (newValue) {
    currentBannerImage.value = newValue
  }
}, { immediate: true })
</script>

<style scoped>
/* 自定义样式 */
.prose {
  color: inherit;
}

.prose p {
  margin-bottom: 1rem;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .sticky {
    position: static;
  }
}
</style>

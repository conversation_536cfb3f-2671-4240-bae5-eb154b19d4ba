<template>
  <SecondaryPageTemplate
    page-title="标志成果"
    page-subtitle="广州南洋理工职业学院"
    breadcrumb-category="学校概况"
    breadcrumb-page="标志成果"
    sidebar-title="学校概况"
    :sidebar-items="sidebarItems"
    :content-title="(schoolInfo.school_name || '学校') + '标志成果'"
    :content-subtitle="'办学成果与发展成就'"
    :banner-image="bannerImage"
  >
    <template #content>
      <!-- 标志成果介绍 -->
      <div class="mb-8" v-if="!loading">
        <p class="text-gray-700 leading-relaxed mb-4">
          学校在办学过程中取得了一系列标志性成果，涵盖办学荣誉、师生成就、教科研项目等多个方面，充分展现了学校的办学实力和发展水平。
        </p>
      </div>

      <!-- 加载状态 -->
      <div class="mb-8 text-center" v-if="loading">
        <div class="inline-flex items-center px-4 py-2 text-gray-600">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载标志成果信息...
        </div>
      </div>

      <!-- 标志成果展示 -->
      <div class="space-y-8" v-if="!loading">
        <!-- 标志性办学成果 -->
        <div class="border-l-4 border-[#C32A31] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">标志性办学成果</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">序号</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">成果名称</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200">
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                  <td class="px-6 py-4 text-sm text-gray-900">全国国防教育特色学校（全国首批中唯一民办高校）</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                  <td class="px-6 py-4 text-sm text-gray-900">"全国民办非企业单位优秀服务品牌"单位</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                  <td class="px-6 py-4 text-sm text-gray-900">全国首届军事训练营"优秀组织"单位</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4</td>
                  <td class="px-6 py-4 text-sm text-gray-900">全国党建工作标杆院系培育创建单位</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                  <td class="px-6 py-4 text-sm text-gray-900">省域高水平高职院校建设计划</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">6</td>
                  <td class="px-6 py-4 text-sm text-gray-900">全省教育系统党建工作先进集体</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">7</td>
                  <td class="px-6 py-4 text-sm text-gray-900">广东省就业工作督查优秀等级</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                  <td class="px-6 py-4 text-sm text-gray-900">广东省人文社科重点研究基地（全省民办高职唯一）</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">9</td>
                  <td class="px-6 py-4 text-sm text-gray-900">广东省"高校思政课建设示范点培育单位"（全省民办高校唯一入选单位）</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">10</td>
                  <td class="px-6 py-4 text-sm text-gray-900">广东省教育研究院——民办教育研究基地</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">11</td>
                  <td class="px-6 py-4 text-sm text-gray-900">广东当代民办学校突出贡献奖</td>
                </tr>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                  <td class="px-6 py-4 text-sm text-gray-900">粤港澳大湾区教育质量评价特色学校</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 师生五年标志性育人成果 -->
        <div class="border-l-4 border-[#991E29] pl-6">
          <h3 class="text-xl font-semibold text-gray-900 mb-6">师生五年标志性育人成果</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 国家级竞赛成果 -->
            <div class="bg-gradient-to-br from-red-50 to-orange-50 p-6 rounded-lg">
              <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                国家级竞赛
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>数学建模竞赛一等奖</span>
                  <span class="font-semibold text-red-600">1项</span>
                </div>
                <div class="flex justify-between">
                  <span>数学建模竞赛二等奖</span>
                  <span class="font-semibold text-red-600">2项</span>
                </div>
                <div class="flex justify-between">
                  <span>技能大赛二等奖</span>
                  <span class="font-semibold text-red-600">1项</span>
                </div>
                <div class="flex justify-between">
                  <span>技能大赛三等奖</span>
                  <span class="font-semibold text-red-600">2项</span>
                </div>
              </div>
            </div>

            <!-- 省级竞赛成果 -->
            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg">
              <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
                省级竞赛
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>技能大赛一等奖</span>
                  <span class="font-semibold text-blue-600">7项</span>
                </div>
                <div class="flex justify-between">
                  <span>技能大赛二等奖</span>
                  <span class="font-semibold text-blue-600">20项</span>
                </div>
                <div class="flex justify-between">
                  <span>技能大赛三等奖</span>
                  <span class="font-semibold text-blue-600">35项</span>
                </div>
                <div class="flex justify-between">
                  <span>挑战杯二等奖</span>
                  <span class="font-semibold text-blue-600">2项</span>
                </div>
              </div>
            </div>

            <!-- 学生个人荣誉 -->
            <div class="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg">
              <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
                学生个人荣誉
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>中国大学生自强之星</span>
                  <span class="font-semibold text-green-600">1人</span>
                </div>
                <div class="flex justify-between">
                  <span>自强之星提名奖</span>
                  <span class="font-semibold text-green-600">3人</span>
                </div>
                <div class="flex justify-between">
                  <span>省级体育竞赛第一名</span>
                  <span class="font-semibold text-green-600">2项</span>
                </div>
                <div class="flex justify-between">
                  <span>国旗护卫队一等奖</span>
                  <span class="font-semibold text-green-600">2项</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 成果总结 -->
        <div class="bg-gradient-to-r from-[#C32A31] to-[#991E29] text-white p-6 rounded-lg">
          <h3 class="text-xl font-semibold mb-4">成果总览</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">12+</div>
              <div class="text-sm opacity-90">标志性办学成果</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">87+</div>
              <div class="text-sm opacity-90">国家级奖项</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">457+</div>
              <div class="text-sm opacity-90">省级奖项</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold mb-1">90+</div>
              <div class="text-sm opacity-90">教科研项目</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </SecondaryPageTemplate>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SecondaryPageTemplate from './SecondaryPageTemplate.vue'

// 响应式数据
const loading = ref(true)
const schoolInfo = ref({})
const bannerImage = ref('')

// 侧边栏导航项目
const sidebarItems = [
  { name: '学校简介', route: { name: 'SchoolIntroduction' } },
  { name: '现任领导', route: { name: 'CurrentLeadership' } },
  { name: '历任领导', route: { name: 'FormerLeadership' } },
  { name: '学校大事记', route: { name: 'SchoolChronicle' } },
  { name: '学校荣誉', route: { name: 'SchoolHonors' } },
  { name: '组织架构', route: { name: 'OrganizationStructure' } },
  { name: '标志成果', route: { name: 'SignificantAchievements' }, active: true }
]

// 组件挂载时设置数据
onMounted(() => {
  // 设置学校信息（硬编码）
  schoolInfo.value = {
    school_name: '广州南洋理工职业学院'
  }

  // 设置本地横幅图片
  bannerImage.value = '/images/banners/school-introduction.jpg'

  // 设置加载完成
  loading.value = false
})
</script>

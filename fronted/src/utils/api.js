import request from './request';

/**
 * 文章相关API
 */
export const articleApi = {
  /**
   * 获取文章列表
   * @param {Object} params - 查询参数
   * @param {number} params.limit - 限制返回的文章数量（映射为pageSize）
   * @param {number} params.page - 页码（映射为pageNum）
   * @param {string} params.category - 文章分类
   * @returns {Promise} - 返回文章列表
   */
  getArticleList: (params = { limit: 100, page: 1 }) => {
    // 将前端参数映射为后端期望的参数名
    const backendParams = {
      pageSize: params.limit || 10,
      pageNum: params.page || 1
    };
    return request.get('/api/articles/latest', { params: backendParams });
  },

  /**
   * 搜索文章
   * @param {string} keyword - 搜索关键词
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise} - 返回搜索结果
   */
  searchArticles: (keyword, pageNum = 1, pageSize = 10) => {
    return request.get('/api/articles/search', {
      params: {
        keyword,
        pageNum,
        pageSize
      }
    });
  },

  /**
   * 管理员搜索文章
   * @param {string} keyword - 搜索关键词
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise} - 返回搜索结果
   */
  adminSearchArticles: (keyword, pageNum = 1, pageSize = 10) => {
    return request.get('/api/admin/articles/search', {
      params: {
        keyword,
        pageNum,
        pageSize
      }
    });
  },

  /**
   * 获取文章详情
   * @param {string|number} id - 文章ID
   * @returns {Promise} - 返回文章详情
   */
  getArticleDetail: (id) => {
    return request.get(`/api/articles/${id}`);
  },

  /**
   * 获取所有可用的文章分类列表
   * @returns {Promise} - 返回分类列表
   */
  getAllCategories: () => {
    return request.get('/api/articles/categories');
  },

  /**
   * 获取所有可用的学院/部门列表
   * @returns {Promise} - 返回学院/部门列表
   */
  getAllSections: () => {
    return request.get('/api/articles/sections');
  },

  /**
   * 根据分类获取文章列表
   * @param {string} category - 文章分类
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise} - 返回文章列表
   */
  getArticlesByCategory: (category, pageNum = 1, pageSize = 10) => {
    return request.get(`/api/articles/category/${encodeURIComponent(category)}`, {
      params: {
        pageNum,
        pageSize
      }
    });
  },

  /**
   * 根据学院/部门获取文章列表
   * @param {string} section - 学院/部门
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise} - 返回文章列表
   */
  getArticlesBySection: (section, pageNum = 1, pageSize = 10) => {
    return request.get(`/api/articles/section/${encodeURIComponent(section)}`, {
      params: {
        pageNum,
        pageSize
      }
    });
  },

  /**
   * 多条件搜索文章
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词（可选）
   * @param {string} params.category - 文章分类（可选）
   * @param {string} params.section - 学院/部门（可选）
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页记录数
   * @returns {Promise} - 返回搜索结果
   */
  searchWithMultipleConditions: (params) => {
    const { keyword, category, section, pageNum = 1, pageSize = 10 } = params;
    return request.get('/api/articles/search/advanced', {
      params: {
        keyword: keyword || undefined,
        category: category || undefined,
        section: section || undefined,
        pageNum,
        pageSize
      }
    });
  },

  /**
   * 创建文章
   * @param {Object} article - 文章数据
   * @returns {Promise} - 返回创建结果
   */
  createArticle: (article) => {
    return request.post('/api/admin/article/new', article);
  },

  /**
   * 更新文章
   * @param {string|number} id - 文章ID
   * @param {Object} article - 文章数据
   * @returns {Promise} - 返回更新结果
   */
  updateArticle: (id, article) => {
    return request.put(`/api/admin/article/${id}`, article);
  },

  /**
   * 删除文章
   * @param {string|number} id - 文章ID
   * @returns {Promise} - 返回删除结果
   */
  deleteArticle: (id) => {
    return request.delete(`/api/admin/article/${id}`);
  },

  /**
   * 获取管理员文章详情
   * @param {string|number} id - 文章ID
   * @returns {Promise} - 返回文章详情
   */
  getAdminArticleDetail: (id) => {
    return request.get(`/api/admin/article/${id}`);
  },

  /**
   * 上传图片
   * @param {File} file - 图片文件
   * @returns {Promise} - 返回上传结果，包含图片URL
   */
  uploadImage: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request.post('/api/admin/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

/**
 * 用户相关API
 */
export const userApi = {
  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名
   * @param {string} loginData.password - 密码
   * @returns {Promise} - 返回登录结果
   */
  login: (loginData) => {
    return request.post('/api/user/login', loginData);
  },

  /**
   * 获取用户权限
   * @returns {Promise} - 返回用户权限信息
   */
  getPermission: () => {
    return request.get('/api/user/permission');
  }
};

/**
 * 首页相关API
 */
export const homeApi = {
  /**
   * 获取校园看点数据
   * @returns {Promise} - 返回校园看点数据
   */
  getCampusHighlights: () => {
    return request.get('/api/home/<USER>');
  },

  /**
   * 获取轮播图数据
   * @returns {Promise} - 返回轮播图数据
   */
  getCarouselData: () => {
    return request.get('/api/home/<USER>');
  },

  /**
   * 搜索文章
   * @param {string} keyword - 搜索关键词
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise} - 返回搜索结果
   */
  searchArticles: (keyword, pageNum = 1, pageSize = 10) => {
    return request.get('/api/articles/search', {
      params: {
        keyword,
        pageNum,
        pageSize
      }
    });
  }
};

export default {
  article: articleApi,
  user: userApi,
  home: homeApi
};

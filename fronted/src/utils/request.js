import axios from 'axios';
import eventBus from './eventBus';

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.PROD ? 'https://preview1.guducat.laffey.cc' : '', // 生产环境使用指定的API地址，开发环境使用代理
  timeout: 5000, // 请求超时时间 - 5秒，平衡速度和可靠性
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('admin_token');
    // 如果有token，则添加到请求头
    if (token) {
      config.headers['satoken'] = token;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (response.status === 200) {
      return response.data;
    }

    console.error('响应错误:', response);
    return Promise.reject(new Error('请求失败'));
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 401表示未授权，可能是token过期或无效
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');

      // 触发登出事件，通知其他组件（特别是Header）更新状态
      eventBus.emit('user-logout');

      // 跳转到登录页面
      window.location.href = '/login?redirect=/admin';
    }

    console.error('响应错误:', error);
    return Promise.reject(error);
  }
);

export default request;

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
      vue(),
      tailwindcss()
  ],

  // 开发服务器配置
  server: {
    proxy: {
      '/api': {  // 所有以 /api 开头的请求都会被代理
        target: 'http://localhost:8080', // 你的 Spring Boot 后端地址
        changeOrigin: true
        // 不重写路径，因为后端 API 也是以 /api 开头的
      }
    }
  },

  // 生产环境构建配置
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    }
  }
})

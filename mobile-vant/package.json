{"name": "mobile-vant", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vant": "^4.8.5", "vue": "^3.4.21", "axios": "^1.9.0"}, "devDependencies": {"@vant/auto-import-resolver": "^1.1.0", "@vitejs/plugin-vue": "^4.3.4", "less": "^4.1.3", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.6", "vue-router": "^4.5.1", "terser": "^5.40.0"}}
<template>
  <!-- 导航栏组件 -->
   <van-sticky>
    <AppNavBar />
  </van-sticky>

  <!-- 主要内容区域 -->
  <RouterView v-slot="{ Component, route }">
    <KeepAlive :include="['SearchPage']">
      <component :is="Component" :key="route.fullPath" />
    </KeepAlive>
  </RouterView>

  <!-- 底部标签栏组件 -->
  <AppTabBar />
</template>

<script setup>
import { KeepAlive } from 'vue'
import { RouterView } from 'vue-router'
import AppNavBar from './components/navigation/AppNavBar.vue'
import AppTabBar from './components/navigation/AppTabBar.vue'
</script>

<style scoped>
/* 全局样式可以在这里添加 */
</style>

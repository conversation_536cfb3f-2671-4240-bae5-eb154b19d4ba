<template>
  <div class="home-page">
    <!-- 轮播图组件 -->
    <swipe />
    
    <!-- 快捷栏 -->
    <Grid />

    <!-- 校园新闻 -->
    <div class="hot-news">
      <div class="section-header">
        <h3 class="section-title">校园新闻</h3>
        <span class="more-link" @click="goToNews">更多 ></span>
      </div>

      <van-loading v-if="loading" class="loading-container" vertical>
        加载中...
      </van-loading>

      <div v-else class="news-list">
        <div
          v-for="item in hotNews"
          :key="item.newsId || item.id"
          class="news-item"
          @click="goToDetail(item.newsId || item.id)"
        >
          <div class="news-content">
            <h4 class="news-title">{{ item.title }}</h4>
            <p class="news-summary">{{
              item.contentPreview ||
              (item.contentText ? item.contentText.substring(0, 60) + '...' : '无内容摘要')
            }}</p>
            <div class="news-meta">
              <span class="category">{{ item.category }}</span>
              <span class="time">{{ formatTime(item.pubTime || item.publishTime) }}</span>
            </div>
          </div>
          <div v-if="item.image || item.imageUrl" class="news-image">
            <van-image
              :src="item.image || item.imageUrl"
              width="80"
              height="60"
              fit="cover"
              radius="6"
              :lazy-load="true"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showFailToast } from 'vant'
import Swipe from './HomePage/Swipe.vue'
import { articleApi } from '../utils/api'
import Grid from './HomePage/Grid.vue'

const router = useRouter()

// 响应式数据
const hotNews = ref([])
const loading = ref(false)

// 格式化时间
const formatTime = (dateString) => {
  if (!dateString) return ''

  const date = new Date(dateString)
  const now = Date.now()
  const diff = now - date.getTime()
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'numeric',
      day: 'numeric'
    })
  }
}

// 获取热门新闻
const fetchHotNews = async () => {
  try {
    loading.value = true

    const result = await articleApi.getArticleList({ limit: 6, page: 1 })

    let articles = []

    if (result && result.code === 200 && result.data) {
      if (Array.isArray(result.data)) {
        articles = result.data
      } else if (result.data.list && Array.isArray(result.data.list)) {
        articles = result.data.list
      }
    } else if (Array.isArray(result)) {
      articles = result
    } else if (result && result.list && Array.isArray(result.list)) {
      articles = result.list
    }

    hotNews.value = articles.slice(0, 6)

  } catch (error) {
    console.error('获取热门新闻失败:', error)
    showFailToast('加载新闻失败')
  } finally {
    loading.value = false
  }
}

// 跳转到新闻列表
const goToNews = () => {
  router.push({ name: 'news-list' })
}

// 跳转到新闻详情
const goToDetail = (newsId) => {
  router.push({ name: 'news-detail', params: { id: newsId } })
}

// 页面加载时获取数据
onMounted(() => {
  fetchHotNews()
})
</script>

<style scoped>
.home-page {
  padding-bottom: 60px; /* TabBar高度 */
  min-height: 100vh;
  background-color: #f7f8fa;
}


.hot-news {
  background: white;
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0;
}

.more-link {
  font-size: 14px;
  color: #1989fa;
  cursor: pointer;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.news-item {
  display: flex;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #ebedf0;
  cursor: pointer;
  transition: all 0.2s;
}

.news-item:active {
  background-color: #f7f8fa;
  transform: scale(0.98);
}

.news-content {
  flex: 1;
  margin-right: 12px;
}

.news-title {
  font-size: 15px;
  font-weight: 600;
  line-height: 1.4;
  color: #323233;
  margin: 0 0 6px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-summary {
  font-size: 13px;
  color: #646566;
  line-height: 1.4;
  margin: 0 0 8px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
}

.news-meta .category {
  color: #C32A31;
  background: #e8f3ff;
  padding: 1px 4px;
  border-radius: 8px;
}

.news-meta .time {
  color: #969799;
}

.news-image {
  flex-shrink: 0;
}
</style>

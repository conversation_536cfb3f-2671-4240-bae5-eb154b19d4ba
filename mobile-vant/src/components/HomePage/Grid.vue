<template>
    <div class="quick-nav">
      <van-grid :column-num="3">
        <van-grid-item 
          v-for="item in quickNavItems" 
          :key="item.id" 
          :text="item.title"
          @click="handleQuickNav(item)"
        >
          <template #icon>
            <div v-if="item.id === 6" v-html="item.icon"></div>
            <van-icon v-else :name="item.icon" size="24" />
          </template>
        </van-grid-item>
      </van-grid>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { showToast } from 'vant'

// 快捷导航数据
const quickNavItems = ref([
  { id: 1, 
    title: '学校简介', 
    icon: 'info-o', 
    action: '/search' },
  { id: 2, 
    title: '学校荣誉', 
    icon: 'award-o', 
    action: '/search' },
  { id: 3, 
    title: '学校组织', 
    icon: 'cluster-o', 
    action: '/search' },
  { id: 4, 
    title: '教务中心', 
    icon: 'records-o', 
    action: '/search' },
  { id: 5, 
    title: '更多详情', 
    icon: 'more-o', 
    action: '/search' },
  { id: 6, 
    title: '党群组织', 
    icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="1 1 32 32" fill="#ed2c25"><path d="M4 14l7.923966-7.923966A5.315073 5.315073 0 0 0 17 5l2.5 2.5-4 4L33 29l-4 4-17.5-17.5-3 3zm.5 8.5a16.62077 16.62077 0 0 0 12 5.12077A11.12077 11.12077 0 0 0 27.62077 16.5 16.62077 16.62077 0 0 0 17 1a16 16 0 0 1 0 32 18 18 0 0 1-14.979984-8.020016zM3 29l2-2 2 2-2 2z"/><circle cx="3.5" cy="30.5" r="2.5"/></svg>`, 
    action: '/search' },
])

// 处理快捷导航点击
const handleQuickNav = (item) => {
  showToast(`点击了${item.title}`)
  // 这里可以根据不同的action进行路由跳转或其他操作
}
</script>

<style scoped>

</style>
<template>
    <!-- 图片轮播 -->
    <van-swipe 
      class="image-swipe" 
      :autoplay="3000" 
      indicator-color="#991E29"
      v-model:current="currentIndex"
    >
      <van-swipe-item v-for="slide in slides" :key="slide.id">
        <img :src="slide.image" :alt="slide.title" class="swipe-image" />
      </van-swipe-item>
    </van-swipe>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { Swipe, SwipeItem } from 'vant'
import axios from 'axios'

const API_BASE_URL = 'http://localhost:8080'
const slides = ref([])
const currentIndex = ref(0)

const fetchHomeSlides = async () => {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/home/<USER>
    slides.value = response.data.map(slide => ({
      id: slide.id,
      title: slide.title,
      description: slide.description,
      image: slide.image_url
    }))
  } catch (error) {
    console.error('获取轮播图数据失败:', error)
  }
}

onMounted(() => {
  fetchHomeSlides()
})
</script>

<style scoped>
.home-page {
  padding: 0;
  margin-top: 0; /* 导航栏高度 */
  min-height: calc(100vh - 46px); /* 确保内容区域填满剩余空间 */
  box-sizing: border-box;
  background: #fff; /* 添加白色背景 */
}

.image-swipe {
  width: 100%;
  height: calc(100vw * 400 / 1440);
  max-height: 400px;
  margin-bottom: 8px;
}

.text-swipe {
  height: 80px;
  margin-bottom: 16px;
  background: #f7f8fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.swipe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swipe-caption {
  padding: 12px;
  text-align: center;
}

.swipe-caption h3 {
  font-size: 16px;
  margin-bottom: 4px;
  color: #333;
}

.swipe-caption p {
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.content-section {
  padding: 0 16px;
}
</style>

<template>
  <div class="user-page">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <van-image
          :src="userInfo.avatar"
          round
          width="60"
          height="60"
          fit="cover"
        />
      </div>
      <div class="user-info">
        <h3 class="username">{{ userInfo.nickname || '未登录' }}</h3>
        <p class="user-desc">{{ userInfo.description || '仅支持在校学生教师登录' }}</p>
      </div>
      <div class="user-action">
        <van-button plain
          v-if="!userInfo.isLoggedIn"
          type="danger"
          size="small"
          round
          @click="goToLogin"
        >
          登录
        </van-button>
        <van-button
          v-else
          type="warning"
          size="small"
          round
          @click="handleLogout"
        >
          退出
        </van-button>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="我的收藏"
          icon="star-o"
          is-link
          :badge="favoriteCount"
          @click="goToFavorites"
        />
        <van-cell
          title="浏览历史"
          icon="clock-o"
          is-link
          @click="goToHistory"
        />
        <van-cell
          title="消息通知"
          icon="bell"
          is-link
          :badge="notificationCount"
          @click="goToNotifications"
        />
      </van-cell-group>
    </div>

    <!-- 设置菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="设置"
          icon="setting-o"
          is-link
          @click="goToSettings"
        />
        <van-cell
          title="关于我们"
          icon="info-o"
          is-link
          @click="goToAbout"
        />
        <van-cell
          title="意见反馈"
          icon="comment-o"
          is-link
          @click="goToFeedback"
        />
      </van-cell-group>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showToast, showSuccessToast } from 'vant'

const router = useRouter()

// 响应式数据
const userInfo = ref({
  isLoggedIn: false,
  nickname: '',
  avatar: './images/NY_RectangularFavicon.png',
  description: ''
})

const favoriteCount = ref(0)
const notificationCount = ref(0)

// 跳转到收藏页面
const goToFavorites = () => {
  if (!userInfo.value.isLoggedIn) {
    showToast('请先登录')
    return
  }
  // TODO: 跳转到收藏页面
  showToast('跳转到收藏页面')
}

// 跳转到历史记录
const goToHistory = () => {
  // TODO: 跳转到历史记录页面
  showToast('跳转到历史记录页面')
}

// 跳转到通知页面
const goToNotifications = () => {
  if (!userInfo.value.isLoggedIn) {
    showToast('请先登录')
    return
  }
  // TODO: 跳转到通知页面
  showToast('跳转到通知页面')
}

// 跳转到设置页面
const goToSettings = () => {
  // TODO: 跳转到设置页面
  showToast('跳转到设置页面')
}

// 跳转到关于我们
const goToAbout = () => {
  router.push({ name: 'about' })
}

// 跳转到意见反馈
const goToFeedback = () => {
  // TODO: 跳转到意见反馈页面
  showSuccessToast('跳转到意见反馈页面')
}

// 跳转到登录页面
const goToLogin = () => {
  router.push({ name: 'login' })
}

// 处理退出登录
const handleLogout = () => {
  showConfirmDialog({
    title: '确认退出',
    message: '确定要退出登录吗？',
    confirmButtonColor: 'red',
  }).then(() => {
    // 清除用户信息
    userInfo.value = {
      isLoggedIn: false,
      nickname: '',
      avatar: './images/NY_RectangularFavicon.png',
      description: ''
    }
    
    // 清除本地存储
    localStorage.removeItem('userInfo')
    
    showSuccessToast('已退出登录')
  }).catch(() => {
    // 用户取消
  })
}

// 页面加载时获取用户信息
onMounted(() => {
  // 从本地存储获取用户信息
  const savedUserInfo = localStorage.getItem('userInfo')
  if (savedUserInfo) {
    userInfo.value = JSON.parse(savedUserInfo)
  }
  
  // 模拟获取徽章数量
  if (userInfo.value.isLoggedIn) {
    favoriteCount.value = 5
    notificationCount.value = 2
  }
})
</script>

<style scoped>
.user-page {
  padding-bottom: 60px; /* TabBar高度 */
  min-height: 100vh;
  background-color: #f7f8fa;
}

.user-card {
  display: flex;
  align-items: center;
  padding: 20px 16px;
  background: white;
  margin-bottom: 8px;
  cursor: pointer;
}

.user-avatar {
  margin-right: 12px;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 4px 0;
}

.user-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.arrow-icon {
  color: #c8c9cc;
}

.menu-section {
  margin-bottom: 8px;
}

.action-section {
  padding: 16px;
}

/* 自定义Cell样式 */
:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-cell__title) {
  font-size: 16px;
}

:deep(.van-cell__left-icon) {
  margin-right: 12px;
  font-size: 18px;
}
</style>

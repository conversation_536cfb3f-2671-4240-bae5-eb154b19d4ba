<template>
  <div class="about-page">
    <!-- 应用信息 -->
    <div class="app-info">
      <div class="app-logo">
        <van-image
          src="./images/NY_RectangularLogo.png"
          width="120"
          height="80"
          fit="contain"
        />
      </div>
      <h1 class="app-name">南洋移动新闻网</h1>
      <p class="app-version">版本 1.0.0</p>
      <p class="app-description">
        广州南洋理工职业学院官方新闻平台<br>
        提供校园最新资讯和服务
      </p>
    </div>

    <!-- 功能介绍 -->
    <div class="feature-section">
      <h3 class="section-title">主要功能</h3>
      <van-cell-group>
        <van-cell
          title="校园新闻"
          label="学校公告、院系动态、学术活动"
          icon="newspaper-o"
        />
        <van-cell
          title="通知公告"
          label="教务处、学工处重要通知"
          icon="bullhorn-o"
        />
        <van-cell
          title="学术活动"
          label="讲座、研讨会、学术会议"
          icon="bookmark-o"
        />
        <van-cell
          title="校园服务"
          label="校历、课程表、成绩查询"
          icon="service-o"
        />
      </van-cell-group>
    </div>

    <!-- 联系我们 -->
    <div class="contact-section">
      <h3 class="section-title">联系我们</h3>
      <van-cell-group>
        <van-cell
          title="学校官网" 
          value="www.gznylg.edu.cn"
          icon="wap-home-o"
          is-link
          @click="openWebsite"
        />
        <van-cell
          title="意见反馈"
          value="<EMAIL>"
          icon="envelop-o"
          is-link
          @click="sendEmail"
        />
      </van-cell-group>
    </div>

    <!-- 法律信息 -->
    <div class="legal-section">
      <h3 class="section-title">法律信息</h3>
      <van-cell-group>
        <van-cell
          title="用户协议"
          icon="description"
          is-link
          @click="showUserAgreement"
        />
        <van-cell
          title="隐私政策"
          icon="shield-o"
          is-link
          @click="showPrivacyPolicy"
        />
        <van-cell
          title="版权声明"
          icon="certificate"
          is-link
          @click="showCopyright"
        />
      </van-cell-group>
    </div>

    <!-- 技术支持 -->
    <div class="tech-section">
      <h3 class="section-title">技术信息</h3>
      <van-cell-group>
        <van-cell title="技术支持" value="程序开发团队" />
        <van-cell title="开发团队" value="孤独豹猫 & 秋意." />
        <van-cell title="数据维护" value="党委宣传部" />
      </van-cell-group>
    </div>

    <!-- 版权信息 -->
    <div class="copyright">
      <p>© 2025 广州南洋理工职业学院 版权所有</p>
      <p>归属：广州南洋理工职业学院</p>
    </div>

    <!-- 弹出层 -->
    <van-popup
      v-model:show="showPopup"
      position="bottom"
      :style="{ height: '60%' }"
    >
      <div class="popup-content">
        <div class="popup-header">
          <h3>{{ popupTitle }}</h3>
          <van-icon name="cross" @click="showPopup = false" />
        </div>
        <div class="popup-body">
          <p>{{ popupContent }}</p>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Image, CellGroup, Cell, Icon, Popup, Toast } from 'vant'

// 响应式数据
const showPopup = ref(false)
const popupTitle = ref('')
const popupContent = ref('')
const lastUpdateTime = ref(new Date().toLocaleDateString('zh-CN'))

// 打开网站
const openWebsite = () => {
  window.open('https://www.example.com', '_blank')
}

// 发送邮件
const sendEmail = () => {
  window.location.href = 'mailto:<EMAIL>'
}

// 拨打电话
const callPhone = () => {
  window.location.href = 'tel:************'
}

// 显示用户协议
const showUserAgreement = () => {
  popupTitle.value = '用户协议'
  popupContent.value = `
    欢迎使用校园新闻网！
    
    1. 服务条款
    本应用为用户提供校园新闻资讯服务，用户在使用本应用时应遵守相关法律法规。
    
    2. 用户责任
    用户应确保提供的信息真实有效，不得发布违法违规内容。
    
    3. 隐私保护
    我们承诺保护用户隐私，不会泄露用户个人信息。
    
    4. 免责声明
    本应用提供的信息仅供参考，我们不对信息的准确性承担责任。
  `
  showPopup.value = true
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  popupTitle.value = '隐私政策'
  popupContent.value = `
    我们非常重视您的隐私保护：
    
    1. 信息收集
    我们只收集为您提供服务所必需的信息。
    
    2. 信息使用
    收集的信息仅用于改善服务质量，不会用于其他目的。
    
    3. 信息保护
    我们采用先进的安全技术保护您的个人信息。
    
    4. 信息共享
    未经您同意，我们不会与第三方分享您的个人信息。
  `
  showPopup.value = true
}

// 显示版权声明
const showCopyright = () => {
  popupTitle.value = '版权声明'
  popupContent.value = `
    版权声明：
    
    1. 本应用的所有内容，包括但不限于文字、图片、音频、视频等，均受版权法保护。
    
    2. 未经授权，任何人不得复制、传播、展示、镜像、上载、下载本应用内容。
    
    3. 本应用中的新闻内容来源于官方渠道，版权归原作者所有。
    
    4. 如有版权问题，请联系我们处理。
  `
  showPopup.value = true
}
</script>

<style scoped>
.about-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.app-info {
  background: white;
  text-align: center;
  padding: 40px 20px;
  margin-bottom: 8px;
}

.app-logo {
  margin-bottom: 16px;
}

.app-name {
  font-size: 24px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 8px 0;
}

.app-version {
  font-size: 14px;
  color: #969799;
  margin: 0 0 12px 0;
}

.app-description {
  font-size: 16px;
  color: #646566;
  line-height: 1.5;
  margin: 0;
}

.feature-section,
.contact-section,
.legal-section,
.tech-section {
  margin-bottom: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  padding: 16px 16px 8px;
  margin: 0;
  background: white;
}

.copyright {
  text-align: center;
  padding: 24px 16px;
  color: #969799;
  font-size: 12px;
  line-height: 1.5;
}

.popup-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
}

.popup-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.popup-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  line-height: 1.6;
  white-space: pre-line;
}

/* 自定义Cell样式 */
:deep(.van-cell__left-icon) {
  color: #ff6b00;
  margin-right: 12px;
  font-size: 18px;
}

:deep(.van-cell__title) {
  font-weight: 500;
}

:deep(.van-cell__label) {
  margin-top: 4px;
}
</style>

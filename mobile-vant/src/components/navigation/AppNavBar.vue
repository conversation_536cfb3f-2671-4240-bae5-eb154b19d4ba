<template>
  <van-nav-bar
    :title="navTitle"
    :left-text="showBackButton ? '返回' : ''"
    :right-text="showMenuButton ? '菜单' : ''"
    :left-arrow="showBackButton"
    :fixed="true"
    :placeholder="true"
    safe-area-inset-top
    @click-left="handleLeftClick"
    @click-right="handleRightClick"
  >
    <!-- 自定义右侧内容插槽 -->
    <template #right v-if="showSearchIcon">
      <van-icon name="search" size="18" @click="handleSearchClick" />
    </template>
  </van-nav-bar>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { NavBar, Icon } from 'vant'

const router = useRouter()
const route = useRoute()

// 响应式数据
const currentRoute = ref(route)

// 监听路由变化
watch(route, (newRoute) => {
  currentRoute.value = newRoute
}, { immediate: true })

// 计算导航栏标题
const navTitle = computed(() => {
  // 优先使用路由meta中的title
  if (currentRoute.value.meta?.title) {
    return currentRoute.value.meta.title
  }

  // 备用标题映射
  const routeName = currentRoute.value.name
  const titleMap = {
    'home': '校园新闻网',
    'search': '搜索',
    'user': '校园事务',
    'news-detail': '新闻详情',
    'news-list': '新闻列表',
    'login': '登录',
    'about': '关于我们'
  }
  return titleMap[routeName] || '校园新闻网'
})

// 判断是否显示返回按钮
const showBackButton = computed(() => {
  // 使用路由meta中的level信息判断
  const level = currentRoute.value.meta?.level || 1
  return level > 1 // 二级及以上页面显示返回按钮
})

// 判断是否显示菜单按钮
const showMenuButton = computed(() => {
  const routeName = currentRoute.value.name
  const level = currentRoute.value.meta?.level || 1

  // 主页不显示菜单按钮
  // return routeName !== 'home'

  //临时禁用此功能，因为没想好怎么展示菜单有什么用
  return false
})

// 判断是否显示搜索图标
const showSearchIcon = computed(() => {
  const routeName = currentRoute.value.name
  const level = currentRoute.value.meta?.level || 1

  // 在一级页面的首页显示搜索图标
  //return level === 1 && routeName === 'home'

  //临时禁用图标，因为这边已经有搜索功能在底部菜单栏常驻
  return false
})

// 处理左侧按钮点击
const handleLeftClick = () => {
  if (showBackButton.value) {
    // 返回上一页
    if (window.history.length > 1) {
      router.back()
    } else {
      // 如果没有历史记录，返回首页
      router.push({ name: 'home' })
    }
  }
}

// 处理右侧按钮点击
const handleRightClick = () => {
  if (showMenuButton.value) {
    // 显示菜单 - 可以实现侧边栏或弹出菜单
    console.log('显示菜单')
    // TODO: 实现菜单功能
  }
}

// 处理搜索图标点击
const handleSearchClick = () => {
  router.push({ name: 'search' })
}
</script>

<style scoped>
/* NavBar 自定义样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(to top, #A0202B 0%, #C32A31 100%);
  color: white;
}

:deep(.van-nav-bar__title) {
  color: white;
  font-weight: 600;
}

:deep(.van-nav-bar__text) {
  color: white;
}

:deep(.van-nav-bar .van-icon) {
  color: white;
}

/* 响应式调整 */
@media (max-width: 375px) {
  :deep(.van-nav-bar__title) {
    font-size: 16px;
  }
}
</style>

<template>
  <van-tabbar 
    v-show="showTabBar"
    route
    active-color="#C02931"
    inactive-color="#7d7e80"
    safe-area-inset-bottom
    :fixed="true"
    :placeholder="true"
    @change="handleTabChange"
  >
    <van-tabbar-item 
      replace 
      to="/" 
      icon="home-o"
      name="home"
    >
      主页
    </van-tabbar-item>
    
    <van-tabbar-item 
      replace 
      to="/search" 
      icon="search"
      name="search"
    >
      搜索
    </van-tabbar-item>
    
    <van-tabbar-item 
      replace 
      to="/user" 
      icon="user-o"
      name="user"
      :badge="userBadge"
    >
      校园事务
    </van-tabbar-item>
  </van-tabbar>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Tabbar, TabbarItem } from 'vant'

const route = useRoute()
const router = useRouter()

// 响应式数据
const currentRoute = ref(route)
const userBadge = ref('') // 用户徽章，可以显示未读消息数量

// 监听路由变化
watch(route, (newRoute) => {
  currentRoute.value = newRoute
}, { immediate: true })

// 判断是否显示TabBar
const showTabBar = computed(() => {
  // 优先使用路由meta中的hideTabBar配置
  if (currentRoute.value.meta?.hideTabBar) {
    return false
  }

  // 根据页面层级判断：只在一级页面显示TabBar
  // const level = currentRoute.value.meta?.level || 1
  // return level === 1
  return true
})

// 处理标签切换
const handleTabChange = (name) => {
  //console.log('切换到标签:', name)
  
  // 可以在这里添加切换时的逻辑
  // 比如埋点统计、页面预加载等
  
  // 滚动到顶部
  setTimeout(() => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }, 100)
}

// 更新用户徽章（可以从外部调用）
const updateUserBadge = (count) => {
  userBadge.value = count > 0 ? count.toString() : ''
}

// 暴露方法给父组件使用
defineExpose({
  updateUserBadge
})
</script>

<style scoped>
/* TabBar 自定义样式 */
:deep(.van-tabbar) {
  background: white;
  border-top: 1px solid #ebedf0;
}

:deep(.van-tabbar-item__text) {
  font-size: 12px;
  font-weight: 500;
}

:deep(.van-tabbar-item--active .van-tabbar-item__text) {
  font-weight: 600;
}

/* 激活状态的图标动画 */
:deep(.van-tabbar-item--active .van-tabbar-item__icon) {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* 徽章样式优化 */
:deep(.van-badge) {
  font-size: 10px;
}
</style>

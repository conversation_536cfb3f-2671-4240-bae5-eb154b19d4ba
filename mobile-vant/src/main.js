import { createApp } from 'vue'
import {
  // 导航组件
  NavBar,
  Tabbar,
  TabbarItem,
  // 基础组件
  Button,
  Cell,
  CellGroup,
  Icon,
  Image,
  Loading,
  Toast,
  Dialog,
  // 表单组件
  Form,
  Field,
  Search,
  Checkbox,
  // 展示组件
  Tag,
  Badge,
  Empty,
  List,
  PullRefresh,
  BackTop,
  // 反馈组件
  Popup,
  ShareSheet,
  DropdownMenu,
  DropdownItem
} from 'vant'
import App from './App.vue'
import router from './router'

const app = createApp(App)

// 注册Vant组件
app.use(NavBar)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Button)
app.use(Cell)
app.use(CellGroup)
app.use(Icon)
app.use(Image)
app.use(Loading)
app.use(Toast)
app.use(Dialog)
app.use(Form)
app.use(Field)
app.use(Search)
app.use(Checkbox)
app.use(Tag)
app.use(Badge)
app.use(Empty)
app.use(List)
app.use(PullRefresh)
app.use(BackTop)
app.use(Popup)
app.use(ShareSheet)
app.use(DropdownMenu)
app.use(DropdownItem)

app.use(router)
app.mount('#app')

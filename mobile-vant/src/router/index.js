import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '../components/HomePage.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage,
    meta: {
      title: '首页',
      level: 1 // 一级页面
    }
  },
  {
    path: '/search',
    name: 'search',
    component: () => import('../components/SearchPage.vue'),
    meta: {
      title: '搜索',
      level: 1 // 一级页面
    }
  },
  {
    path: '/user',
    name: 'user',
    component: () => import('../components/UserPage.vue'),
    meta: {
      title: '个人中心',
      level: 1 // 一级页面
    }
  },
  {
    path: '/news',
    name: 'news-list',
    component: () => import('../components/NewsListPage.vue'),
    meta: {
      title: '最新新闻',
      level: 2 // 二级页面
    }
  },
  {
    path: '/news/:id',
    name: 'news-detail',
    component: () => import('../components/NewsDetailPage.vue'),
    props: true,
    meta: {
      title: '新闻详情',
      level: 2 // 二级页面
    }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../components/LoginPage.vue'),
    meta: {
      title: '登录',
      level: 2, // 二级页面
      hideTabBar: true // 隐藏底部导航
    }
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../components/UserPage/AboutPage.vue'),
    meta: {
      title: '关于我们',
      level: 2, // 二级页面
      hideTabBar: true // 隐藏底部导航
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router

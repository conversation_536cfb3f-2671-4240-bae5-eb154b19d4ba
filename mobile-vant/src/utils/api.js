import request from './request'

/**
 * 文章相关API
 */
export const articleApi = {
  /**
   * 获取文章列表
   * @param {Object} params - 查询参数
   * @param {number} params.limit - 限制返回的文章数量（映射为pageSize）
   * @param {number} params.page - 页码（映射为pageNum）
   * @param {string} params.category - 文章分类
   * @returns {Promise} - 返回文章列表
   */
  getArticleList: (params = { limit: 10, page: 1 }) => {
    // 将前端参数映射为后端期望的参数名
    const backendParams = {
      pageSize: params.limit || 10,
      pageNum: params.page || 1
    }
    return request.get('/api/articles/latest', { params: backendParams })
  },

  /**
   * 根据ID获取文章详情
   * @param {string|number} id - 文章ID
   * @returns {Promise} - 返回文章详情
   */
  getArticleDetail: (id) => {
    return request.get(`/api/articles/${id}`)
  },

  /**
   * 搜索文章
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页记录数
   * @returns {Promise} - 返回搜索结果
   */
  searchArticles: (params) => {
    return request.get('/api/articles/search', { params })
  },

  /**
   * 多条件搜索文章
   * @param {Object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.category - 文章分类
   * @param {string} params.section - 学院/部门
   * @param {number} params.pageNum - 页码
   * @param {number} params.pageSize - 每页记录数
   * @returns {Promise} - 返回搜索结果
   */
  searchWithMultipleConditions: (params) => {
    return request.get('/api/articles/search/advanced', { params })
  },

  /**
   * 获取所有可用的文章分类列表
   * @returns {Promise} - 返回分类列表
   */
  getAllCategories: () => {
    return request.get('/api/articles/categories')
  },

  /**
   * 获取所有可用的学院/部门列表
   * @returns {Promise} - 返回学院/部门列表
   */
  getAllSections: () => {
    return request.get('/api/articles/sections')
  },

  /**
   * 根据分类获取文章列表
   * @param {string} category - 文章分类
   * @param {number} pageNum - 页码
   * @param {number} pageSize - 每页记录数
   * @returns {Promise} - 返回文章列表
   */
  getArticlesByCategory: (category, pageNum = 1, pageSize = 10) => {
    return request.get(`/api/articles/category/${encodeURIComponent(category)}`, {
      params: {
        pageNum,
        pageSize
      }
    })
  }
}

/**
 * 用户相关API
 */
export const userApi = {
  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名
   * @param {string} loginData.password - 密码
   * @returns {Promise} - 返回登录结果
   */
  login: (loginData) => {
    return request.post('/api/user/login', loginData)
  },

  /**
   * 获取用户权限
   * @returns {Promise} - 返回用户权限信息
   */
  getPermission: () => {
    return request.get('/api/user/permission')
  },

  /**
   * 用户登出
   * @returns {Promise} - 返回登出结果
   */
  logout: () => {
    return request.post('/api/user/logout')
  }
}

/**
 * 首页相关API
 */
export const homeApi = {
  /**
   * 获取校园看点数据
   * @returns {Promise} - 返回校园看点数据
   */
  getCampusHighlights: () => {
    return request.get('/api/home/<USER>')
  },

  /**
   * 获取轮播图数据
   * @returns {Promise} - 返回轮播图数据
   */
  getCarouselData: () => {
    return request.get('/api/home/<USER>')
  },

  /**
   * 获取首页轮播图数据
   * @returns {Promise} - 返回首页轮播图数据
   */
  getHomeSlides: () => {
    return request.get('/api/home/<USER>')
  },

  /**
   * 获取中间轮播图数据
   * @returns {Promise} - 返回中间轮播图数据
   */
  getMiddleSlides: () => {
    return request.get('/api/home/<USER>')
  },

  /**
   * 获取英雄区域数据
   * @returns {Promise} - 返回英雄区域数据
   */
  getHeroSection: () => {
    return request.get('/api/home/<USER>')
  }
}

export default {
  article: articleApi,
  user: userApi,
  home: homeApi
}

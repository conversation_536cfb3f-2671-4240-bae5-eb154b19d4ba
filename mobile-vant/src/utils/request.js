import axios from 'axios'
import { Toast } from 'vant'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.PROD ? 'https://preview1.guducat.laffey.cc' : '', // 生产环境使用指定的API地址，开发环境使用代理
  timeout: 10000, // 请求超时时间 - 10秒，适合移动端网络环境
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('admin_token')
    // 如果有token，则添加到请求头
    if (token) {
      config.headers['satoken'] = token
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    Toast.fail('网络请求失败')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 直接返回响应数据
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理不同的错误状态
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          Toast.fail('登录已过期，请重新登录')
          // 清除token
          localStorage.removeItem('admin_token')
          // 可以在这里跳转到登录页
          break
        case 403:
          Toast.fail('没有权限访问')
          break
        case 404:
          Toast.fail('请求的资源不存在')
          break
        case 500:
          Toast.fail('服务器内部错误')
          break
        default:
          Toast.fail(data?.message || '请求失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      Toast.fail('请求超时，请检查网络连接')
    } else {
      Toast.fail('网络连接失败')
    }
    
    return Promise.reject(error)
  }
)

export default request

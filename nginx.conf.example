# Nginx配置示例 - 移动端网站安全配置
# 将此文件内容添加到您的Nginx配置中

server {
    listen 443 ssl http2;
    server_name m.college.guducat.cn;

    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 网站根目录
    root /path/to/mobile-vant/dist;
    index index.html;

    # 安全响应头
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=()" always;
    
    # 内容安全策略
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https://preview1.guducat.laffey.cc https:; media-src 'self' https:; object-src 'none'; base-uri 'self';" always;

    # 隐藏服务器版本信息
    server_tokens off;
    more_set_headers "Server: WebServer";

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff always;
    }

    # Vue Router 历史模式支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 防止缓存HTML文件
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # API代理（如果需要）
    location /api/ {
        proxy_pass https://preview1.guducat.laffey.cc;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 禁用缓存API响应
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 安全配置
    location ~ /\. {
        deny all;
    }

    # 限制文件上传大小
    client_max_body_size 10M;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name m.college.guducat.cn;
    return 301 https://$server_name$request_uri;
}
